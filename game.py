#!/usr/bin/env python3
"""
Beetle Adventure Game
A text-based RPG adventure game
"""

import random
import time
import sys
import os
import json
from typing import Dict, List, Optional

class Character:
    def __init__(self, name: str, character_class: str, gender: str):
        self.name = name
        self.character_class = character_class
        self.gender = gender  # "male" or "female"
        self.level = 1
        self.max_health = 100
        self.health = self.max_health
        self.experience = 0
        self.experience_to_next = 100
        self.inventory = []
        self.gold = 150
        
        # Set base stats based on class
        if character_class == "Warrior":
            self.strength = 15
            self.defense = 10
            self.magic = 5
        elif character_class == "Mage":
            self.strength = 8
            self.defense = 6
            self.magic = 15
        elif character_class == "Rogue":
            self.strength = 12
            self.defense = 8
            self.magic = 8
        else:
            self.strength = 10
            self.defense = 8
            self.magic = 8
    
    def level_up(self):
        self.level += 1
        self.max_health += 20
        self.health = self.max_health
        self.strength += 2
        self.defense += 2
        self.magic += 2
        self.experience_to_next = self.level * 100
        print(f"\n🎉 {self.name} leveled up to level {self.level}!")
        print(f"Health: {self.max_health}, Strength: {self.strength}, Defense: {self.defense}, Magic: {self.magic}")
    
    def gain_experience(self, amount: int):
        self.experience += amount
        if self.experience >= self.experience_to_next:
            self.level_up()
    
    def heal(self, amount: int):
        self.health = min(self.max_health, self.health + amount)
    
    def is_alive(self) -> bool:
        return self.health > 0

class Enemy:
    def __init__(self, name: str, health: int, strength: int, defense: int, experience_reward: int, gold_reward: int):
        self.name = name
        self.health = health
        self.max_health = health
        self.strength = strength
        self.defense = defense
        self.experience_reward = experience_reward
        self.gold_reward = gold_reward
    
    def is_alive(self) -> bool:
        return self.health > 0

class Game:
    def __init__(self):
        self.player = None
        self.current_room = "town"
        self.game_running = True
        self.room_enemy_state = {}  # Track alive enemies per room
        self.dragon_event_triggered = False
        self.dragon_event_defeated = False
        self.boss_dragon_stats = Enemy("Dragon", 100, 20, 15, 100, 100)
        self.save_file = "savegame.txt"
        self.village_upgrade_free = True  # Track if first upgrade is free
        self.village_orc_event_triggered = False  # Track if orc event has happened
        self.village_orcs_defeated = False  # Track if orcs were defeated
        self.moat_serpents_remaining = 2  # Only two serpents in the moat
        self.visited_rooms = set(["town"])  # Track visited rooms
        self.quest_metal_bought = False  # Track if metal was bought for shield upgrade
        self.quest_plant_collected = False  # Track if glowing plant was collected
        self.quest_plant_available = False  # Track if plant quest is active
        self.deep_dungeon_cleared = False  # Track if deep dungeon is cleared
        self.king_defeated = False  # Track if king is defeated
        self.crown_fixed = False  # Track if crown was fixed by blacksmith
        self.player_crowned = False  # Track if player was crowned
        # --- Web integration ---
        self.events: List[str] = []  # Store emitted text lines for API consumption
        self.interactive: bool = True  # When False, suppress direct terminal printing
        
        # Game data
        self.enemies = {
            "goblin": Enemy("Goblin", 30, 8, 5, 25, 15),
            "orc": Enemy("Orc", 50, 12, 8, 40, 25),
            "skeleton": Enemy("Skeleton", 25, 10, 6, 30, 20),
            "bandit": Enemy("Bandit", 35, 11, 7, 35, 30),
            "troll": Enemy("Troll", 80, 16, 12, 80, 50),
            "serpent": Enemy("Serpent", 60, 14, 10, 70, 45)
        }
        
        self.rooms = {
            "town": {
                "description": "You are in a peaceful town. The streets are cobblestone and lined with shops.",
                "exits": ["forest", "cave", "shop"],
                "enemies": []
            },
            "forest": {
                "description": "A dense forest with tall trees. You hear rustling in the bushes.",
                "exits": ["town", "cave"],
                "enemies": ["goblin", "bandit"]
            },
            "cave": {
                "description": "A dark cave with mysterious sounds echoing from within.",
                "exits": ["town", "forest", "dungeon"],
                "enemies": ["skeleton", "orc"]
            },
            "dungeon": {
                "description": "A deep dungeon with ancient stone walls. Danger lurks in the shadows. You notice a passage leading deeper into the dungeon.",
                "exits": ["cave", "deep_dungeon"],
                "enemies": ["orc", "skeleton"]
            },
            "deep_dungeon": {
                "description": "The deepest part of the dungeon. Ancient magic lingers in the air. This place feels like the heart of darkness.",
                "exits": ["dungeon"],
                "enemies": []
            },
            "shop": {
                "description": "A cozy shop filled with various items and potions.",
                "exits": ["town"],
                "enemies": []
            },
            "road": {
                "description": "A long, winding road stretches into the distance. Adventure awaits ahead!",
                "exits": ["town", "field", "village"],
                "enemies": ["bandit", "orc"]
            },
            "field": {
                "description": "A wide open field with tall grass and wildflowers. You feel a gentle breeze.",
                "exits": ["road"],
                "enemies": ["goblin", "skeleton"]
            },
            "village": {
                "description": "A peaceful village with friendly blacksmiths. You can upgrade your sword here.",
                "exits": ["road"],
                "enemies": []
            },
            "bridge": {
                "description": "A rickety old bridge spans a deep chasm. You hear something lurking beneath...",
                "exits": ["village", "moat"],
                "enemies": ["troll"]
            },
            "moat": {
                "description": "A dark moat surrounds the castle. You see a small boat tied to the shore. The water looks murky and dangerous...",
                "exits": ["bridge", "castle_garden"],
                "enemies": ["serpent"]
            },
            "castle_garden": {
                "description": "A once-beautiful garden now overrun with weeds and darkness. Ancient statues stand guard among the shadows.",
                "exits": ["moat", "dining_hall"],
                "enemies": ["skeleton"]
            },
            "dining_hall": {
                "description": "A grand dining hall with long tables and chandeliers. The air is thick with the smell of old feasts and danger.",
                "exits": ["castle_garden", "throne_room"],
                "enemies": ["orc"]
            },
            "throne_room": {
                "description": "The magnificent throne room of the castle. Golden light filters through stained glass windows. This feels like the heart of power.",
                "exits": ["dining_hall"],
                "enemies": []
            }
        }
        
        self.items = {
            "health_potion": {"name": "Health Potion", "cost": 20, "effect": "heal", "value": 30},
            "wooden_sword": {"name": "Wooden Sword", "cost": 30, "effect": "strength", "value": 3},
            "shield": {"name": "Wooden Shield", "cost": 30, "effect": "defense", "value": 3},
            "magic_staff": {"name": "Magic Staff", "cost": 60, "effect": "magic", "value": 5},
            "axe": {"name": "Axe", "cost": 120, "effect": "axe", "value": 15, "unlocked": False},
            "metal": {"name": "Metal", "cost": 5, "effect": "quest", "value": 0}
        }
        
        # Sword upgrade tiers
        self.sword_tiers = {
            "Wooden Sword": {"next": "Steel Sword", "cost": 100, "strength": 5},
            "Steel Sword": {"next": "Iron Sword", "cost": 200, "strength": 8},
            "Iron Sword": {"next": "Crystal Sword", "cost": 400, "strength": 12},
            "Crystal Sword": {"next": "Legendary Sword", "cost": 1050, "strength": 18},
            "Legendary Sword": {"next": None, "cost": 0, "strength": 25}
        }
        
        # Shield upgrade tiers
        self.shield_tiers = {
            "Wooden Shield": {"next": "Steel Shield", "cost": 100, "defense": 5},
            "Steel Shield": {"next": "Iron Shield", "cost": 200, "defense": 8},
            "Iron Shield": {"next": "Crystal Shield", "cost": 400, "defense": 12},
            "Crystal Shield": {"next": "Legendary Shield", "cost": 1050, "defense": 18},
            "Legendary Shield": {"next": None, "cost": 0, "defense": 25}
        }
        
        # Staff upgrade tiers
        self.staff_tiers = {
            "Magic Staff": {"next": "Enhanced Staff", "cost": 0, "magic": 8},
            "Enhanced Staff": {"next": "Mythic Staff", "cost": 0, "magic": 12},
            "Mythic Staff": {"next": "Staff of Legends", "cost": 2000, "magic": 20},
            "Staff of Legends": {"next": None, "cost": 0, "magic": 30}
        }
        
        # Initialize room enemy state
        for room, data in self.rooms.items():
            self.room_enemy_state[room] = list(data["enemies"])
    
    def print_slow(self, text: str, delay: float = 0.03):
        """Print text with a typewriter effect"""
        for char in text:
            print(char, end='', flush=True)
            time.sleep(delay)
        print()
    
    def clear_screen(self):
        """Clear the terminal screen"""
        print("\033[2J\033[H", end="")

    # -----------------------------------------------------------------
    # Output helper – records game text for HTTP layer while optionally
    # printing to the interactive terminal. This will gradually replace
    # direct print() calls so the core logic is UI-agnostic.
    # -----------------------------------------------------------------
    def emit(self, text: str, end: str = "\n"):
        """Store text in self.events and, if interactive, print it."""
        # Save for API consumers
        self.events.append(text + ("" if end == "" else end))
        # Still show in terminal when game is run locally
        if self.interactive:
            print(text, end=end, flush=True)
    
    def show_title(self):
        """Display the game title"""
        title = """
╔══════════════════════════════════════════════════════════════╗
║                    🐛 BEETLE ADVENTURE 🐛                    ║
║                     Text-Based RPG Game                      ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(title)
    
    def create_character(self):
        """Character creation process"""
        self.clear_screen()
        self.show_title()
        
        print("🎮 Welcome to Beetle Adventure!")
        print("Let's create your character...\n")
        
        # Get character name
        while True:
            name = input("Enter your character's name: ").strip()
            if name:
                break
            print("Please enter a valid name.")
        
        # Choose character class
        print("\nChoose your character class:")
        print("1. Warrior - High strength and defense")
        print("2. Mage - High magic power")
        print("3. Rogue - Balanced stats")
        
        while True:
            choice = input("Enter your choice (1-3): ").strip()
            if choice == "1":
                character_class = "Warrior"
                break
            elif choice == "2":
                character_class = "Mage"
                break
            elif choice == "3":
                character_class = "Rogue"
                break
            else:
                print("Please enter 1, 2, or 3.")
        
        # Choose gender
        print("\nChoose your gender:")
        print("1. Male")
        print("2. Female")
        
        while True:
            choice = input("Enter your choice (1-2): ").strip()
            if choice == "1":
                gender = "male"
                break
            elif choice == "2":
                gender = "female"
                break
            else:
                print("Please enter 1 or 2.")
        
        self.player = Character(name, character_class, gender)
        print(f"\n✨ {self.player.name} the {self.player.character_class} has been created!")
        print(f"Health: {self.player.health}, Strength: {self.player.strength}, Defense: {self.player.defense}, Magic: {self.player.magic}")
        input("\nPress Enter to continue...")
    
    def show_status(self):
        """Display player status"""
        title = "King " if self.player_crowned and self.player.gender == "male" else ""
        if self.player_crowned and self.player.gender == "female":
            title = "Queen "
        self.emit(f"\n{'='*50}")
        self.emit(f"👤 {title}{self.player.name} (Level {self.player.level} {self.player.character_class})")
        self.emit(f"❤️  Health: {self.player.health}/{self.player.max_health}")
        self.emit(f"⚔️  Strength: {self.player.strength} | 🛡️  Defense: {self.player.defense} | 🔮 Magic: {self.player.magic}")
        self.emit(f"⭐ Experience: {self.player.experience}/{self.player.experience_to_next}")
        self.emit(f"💰 Gold: {self.player.gold}")
        self.emit(f"🎒 Inventory: {', '.join(self.player.inventory) if self.player.inventory else 'Empty'}")
        self.emit(f"📍 Location: {self.current_room.title()}")
        self.emit(f"{'='*50}")
    
    def show_room(self):
        """Display current room information"""
        room = self.rooms[self.current_room]
        self.visited_rooms.add(self.current_room)
        # Dynamically add/remove exits based on events
        exits = list(room["exits"])
        if self.current_room in ["town", "forest"] and hasattr(self, "player") and self.player and self.player.level >= 6:
            if "road" not in exits:
                exits.append("road")
        if self.current_room == "road":
            if "field" not in exits:
                exits.append("field")
            if "village" not in exits:
                exits.append("village")
        if self.current_room == "field":
            exits = [e for e in exits if e == "road"]
        if self.current_room == "village":
            exits = [e for e in exits if e == "road"]
            # Add bridge exit if orcs were defeated
            if self.village_orcs_defeated and "bridge" not in exits:
                exits.append("bridge")
            # Trigger orc event if first upgrade has been done and event not yet triggered
            if not self.village_upgrade_free and not self.village_orc_event_triggered:
                self.emit("\n⚔️  The village is under attack by three orcs!")
                self.village_orc_event_triggered = True
                self.village_orc_battle()
        if self.current_room == "bridge":
            # Always spawn a troll when entering the bridge
            self.emit("\n🧌 A troll appears and blocks your path!")
            troll = Enemy(
                self.enemies["troll"].name,
                self.enemies["troll"].max_health,
                self.enemies["troll"].strength,
                self.enemies["troll"].defense,
                self.enemies["troll"].experience_reward,
                self.enemies["troll"].gold_reward
            )
            return troll, "troll"
        if self.current_room == "moat":
            if self.moat_serpents_remaining > 0:
                self.emit("\n🚣 You board the small boat to cross the moat...")
                self.emit("🐍 A serpent rises from the dark waters!")
                serpent = Enemy(
                    self.enemies["serpent"].name,
                    self.enemies["serpent"].max_health,
                    self.enemies["serpent"].strength,
                    self.enemies["serpent"].defense,
                    self.enemies["serpent"].experience_reward,
                    self.enemies["serpent"].gold_reward
                )
                return serpent, "serpent"
            else:
                self.emit("\nThe moat is calm. No more serpents remain.")
        # Castle garden skeleton battle - only if king not defeated
        if self.current_room == "castle_garden" and not hasattr(self, 'garden_cleared') and not self.king_defeated:
            self.emit("\n💀 Four skeletons rise from the shadows!")
            self.garden_skeleton_battle()
            return None, None
        # Dining hall orc battle - only if king not defeated
        if self.current_room == "dining_hall" and not hasattr(self, 'dining_cleared') and not self.king_defeated:
            self.emit("\n⚔️  Four orcs emerge from the shadows of the dining hall!")
            self.dining_orc_battle()
            return None, None
        # Deep dungeon boss battle - only accessible after throne room riddle
        if self.current_room == "deep_dungeon" and not self.deep_dungeon_cleared and hasattr(self, 'throne_riddle_shown'):
            self.emit("\n🔥 A mighty dragon, a troll, and two orcs stand before you!")
            self.deep_dungeon_battle()
            return None, None
        # Final king battle in throne room
        if self.current_room == "throne_room" and self.deep_dungeon_cleared and not self.king_defeated and hasattr(self, 'throne_riddle_shown'):
            self.emit("\n👑 The King reveals his true form - an overgrown demon!")
            self.king_battle()
            return None, None
        # Dragon boss event in town
        if self.current_room == "town" and self.dragon_event_triggered and not self.dragon_event_defeated:
            self.emit("\n🔥 A DRAGON attacks the town! Boss fight!")
            enemy = Enemy(
                self.boss_dragon_stats.name,
                self.boss_dragon_stats.max_health,
                self.boss_dragon_stats.strength,
                self.boss_dragon_stats.defense,
                self.boss_dragon_stats.experience_reward,
                self.boss_dragon_stats.gold_reward
            )
            return enemy, "dragon_boss"
        alive_enemies = self.room_enemy_state.get(self.current_room, [])
        if alive_enemies:
            enemy_name = random.choice(alive_enemies)
            enemy = Enemy(
                self.enemies[enemy_name].name,
                self.enemies[enemy_name].max_health,
                self.enemies[enemy_name].strength,
                self.enemies[enemy_name].defense,
                self.enemies[enemy_name].experience_reward,
                self.enemies[enemy_name].gold_reward
            )
            print(f"\n⚠️  A {enemy.name} appears!")
            return enemy, enemy_name
        # Always print room description
        print(f"\n🏠 {self.current_room.upper()}")
        # Special descriptions for restored castle
        if self.king_defeated:
            if self.current_room == "castle_garden":
                print("A beautiful garden restored to its former glory. The statues now gleam with life and the flowers bloom brightly.")
            elif self.current_room == "dining_hall":
                print("A magnificent dining hall restored to its splendor. The chandeliers sparkle and the tables are set for a grand feast.")
            elif self.current_room == "throne_room":
                print("The magnificent throne room, now restored to its full glory. Golden light streams through the windows, and the throne gleams with power.")
            else:
                print(f"{room['description']}")
        else:
            # Special cave description when plant quest is active
            if self.current_room == "cave" and self.quest_plant_available and not self.quest_plant_collected:
                print("A dark cave with mysterious sounds echoing from within. You notice a glowing plant in the corner.")
            else:
                print(f"{room['description']}")
        print(f"Exits: {', '.join(exits)}")
        # Throne room riddle event
        if self.current_room == "throne_room" and not hasattr(self, 'throne_riddle_shown'):
            print("\nYou see a stone slab with writing carved on it:")
            print('''\n"To claim the helm that kings once wore,\nSeek where shadows fall once more.\nBut tread not paths where daylight stays;\nFind the open mouth where the crown still waits,\nAnd save the threads of tangled fates.\n\nTake the path that gleams with gold,\nTo swiftly reach the cellar old."''')
            print("\nThe slab moves away, revealing a hidden passage.")
            print("A hidden passage opened. Where does it lead?")
            self.throne_riddle_shown = True
        return None, None
    
    def combat(self, enemy: Enemy, enemy_id: str):
        """Combat system"""
        title = "King " if self.player_crowned and self.player.gender == "male" else ""
        if self.player_crowned and self.player.gender == "female":
            title = "Queen "
        print(f"\n⚔️  COMBAT: {title}{self.player.name} vs {enemy.name}")
        
        has_axe = "Axe" in self.player.inventory
        while enemy.is_alive() and self.player.is_alive():
            # Player's turn
            print(f"\n{title}{self.player.name}: {self.player.health}/{self.player.max_health} HP")
            print(f"{enemy.name}: {enemy.health}/{enemy.max_health} HP")
            
            print("\nChoose your action:")
            print("1. Attack")
            print("2. Use Magic")
            print("3. Try to flee")
            if has_axe:
                print("4. Attack with Axe 🪓")
            
            choice = input("Enter your choice (1-3" + ("/4" if has_axe else "") + "): ").strip()
            
            if choice == "1":
                # Physical attack
                damage = max(1, self.player.strength - enemy.defense)
                enemy.health -= damage
                print(f"💥 You attack for {damage} damage!")
                
            elif choice == "2":
                # Magic attack
                damage = max(1, self.player.magic - enemy.defense)
                enemy.health -= damage
                print(f"🔮 You cast a spell for {damage} damage!")
                
            elif choice == "3":
                # Flee: move to a random adjacent room and lose 50 experience
                print("🏃 You attempt to flee!")
                current_exits = self.rooms[self.current_room]["exits"]
                # Prevent fleeing to throne room before final battle
                if self.deep_dungeon_cleared and not self.king_defeated:
                    current_exits = [e for e in current_exits if e != "throne_room"]
                if current_exits:
                    new_room = random.choice(current_exits)
                    print(f"You escape to {new_room} but lose 50 experience points!")
                    self.player.experience = max(0, self.player.experience - 50)
                    self.current_room = new_room
                    input("Press Enter to continue...")
                    return False  # Do NOT trigger game over
                else:
                    print("No way to escape!")
            
            elif choice == "4" and has_axe:
                damage = max(1, 20 + self.player.strength - enemy.defense)
                enemy.health -= damage
                print(f"🪓 You swing the Axe for {damage} massive damage!")
            
            # Enemy's turn
            if enemy.is_alive():
                damage = max(1, enemy.strength - self.player.defense)
                self.player.health -= damage
                print(f"💀 {enemy.name} attacks for {damage} damage!")
        
        # Combat result
        if self.player.is_alive():
            print(f"\n🎉 You defeated the {enemy.name}!")
            self.player.gain_experience(enemy.experience_reward)
            self.player.gold += enemy.gold_reward
            print(f"Gained {enemy.experience_reward} experience and {enemy.gold_reward} gold!")
            # Remove enemy from room's alive list or handle special events
            if self.current_room == "moat" and enemy_id == "serpent":
                self.moat_serpents_remaining -= 1
            if enemy_id == "dragon_boss":
                self.dragon_event_defeated = True
                print("🏆 You saved the town from the dragon!")
            elif enemy_id and enemy_id in self.room_enemy_state[self.current_room]:
                self.room_enemy_state[self.current_room].remove(enemy_id)
            return True
        else:
            print(f"\n💀 You were defeated by the {enemy.name}!")
            return False
    
    def shop(self):
        """Shop system"""
        print("\n🏪 WELCOME TO THE SHOP")
        print("Available items:")
        # Show Axe only if dragon is defeated
        for item_id, item in self.items.items():
            if item_id == "axe" and not self.dragon_event_defeated:
                continue
            # Enforce max 3 swords
            sword_count = sum(1 for item in self.player.inventory if "Sword" in item)
            if "sword" in item_id and sword_count >= 3:
                continue
            print(f"{item_id}: {item['name']} - {item['cost']} gold ({item['effect']} +{item['value']})")
        
        print("\nWhat would you like to buy? (or 'exit' to leave)")
        choice = input("Enter item name: ").strip().lower()
        
        if choice == "exit":
            return
        
        if choice in self.items:
            item = self.items[choice]
            if choice == "axe" and not self.dragon_event_defeated:
                print("❌ The Axe is not available yet!")
                return
            # Enforce max 3 swords
            sword_count = sum(1 for item in self.player.inventory if "Sword" in item)
            if "sword" in choice and sword_count >= 3:
                print("❌ You can't carry more than 3 swords!")
                return
            if self.player.gold >= item['cost']:
                self.player.gold -= item['cost']
                self.player.inventory.append(item['name'])
                print(f"✅ You bought {item['name']}!")
                
                # Apply item effect
                if item['effect'] == "heal":
                    self.player.heal(item['value'])
                    print(f"❤️  Restored {item['value']} health!")
                elif item['effect'] == "strength":
                    self.player.strength += item['value']
                    print(f"⚔️  Strength increased by {item['value']}!")
                elif item['effect'] == "defense":
                    self.player.defense += item['value']
                    print(f"🛡️  Defense increased by {item['value']}!")
                elif item['effect'] == "magic":
                    self.player.magic += item['value']
                    print(f"🔮 Magic increased by {item['value']}!")
                elif item['effect'] == "axe":
                    print(f"🪓 You now wield the mighty Axe! Use it in battle for massive damage.")
                elif item['effect'] == "quest":
                    print(f"📦 You got {item['name']} for a quest!")
                    self.quest_metal_bought = True
            else:
                print("❌ Not enough gold!")
        else:
            print("❌ Item not found!")
    
    def move(self, direction: str):
        """Move to a different room"""
        room = self.rooms[self.current_room]
        # Dynamically determine valid exits
        exits = list(room["exits"])
        if self.current_room in ["town", "forest"] and self.player and self.player.level >= 6:
            if "road" not in exits:
                exits.append("road")
        if self.current_room == "road":
            if "field" not in exits:
                exits.append("field")
            if "village" not in exits:
                exits.append("village")
        if self.current_room == "village":
            exits = [e for e in exits if e == "road"]
            # Add bridge exit if orcs were defeated
            if self.village_orcs_defeated and "bridge" not in exits:
                exits.append("bridge")
        if direction in exits:
            prev_room = self.current_room
            self.current_room = direction
            print(f"🚶 You moved to {direction}.")
            # Reset enemies if leaving and returning (except bridge and moat - always have enemies)
            if prev_room != direction and direction in self.rooms and self.rooms[direction]["enemies"] and direction not in ["bridge", "moat"]:
                self.room_enemy_state[direction] = list(self.rooms[direction]["enemies"])
        else:
            print("❌ You can't go that way!")
    
    def show_help(self):
        """Show game commands"""
        print("\n📖 GAME COMMANDS:")
        print("look - Look around the current room")
        print("move <direction> - Move to another room")
        print("status - Show your character status")
        print("shop - Visit the shop (only in town)")
        print("upgrade - Upgrade your equipment (only in village)")
        print("talk - Talk to the blacksmith (only in village)")
        print("rooms - Show all rooms and which you have visited")
        print("take plant - Take the glowing plant (when available)")
        print("save - Save your game")
        print("load - Load your game")
        print("help - Show this help message")
        print("quit - Exit the game")
        print("\n🏰 CASTLE AREAS:")
        print("move moat - Cross the moat (serpent battle)")
        print("move castle_garden - Enter castle garden (skeletons)")
        print("move dining_hall - Enter dining hall (orcs)")
        print("move throne_room - Enter throne room")
    
    def save_game(self):
        if not self.player:
            print("No game in progress to save.")
            return
        data = {
            "name": self.player.name,
            "character_class": self.player.character_class,
            "gender": self.player.gender,
            "level": self.player.level,
            "max_health": self.player.max_health,
            "health": self.player.health,
            "experience": self.player.experience,
            "experience_to_next": self.player.experience_to_next,
            "strength": self.player.strength,
            "defense": self.player.defense,
            "magic": self.player.magic,
            "gold": self.player.gold,
            "inventory": self.player.inventory,
            "current_room": self.current_room,
            "room_enemy_state": self.room_enemy_state,
            "dragon_event_triggered": self.dragon_event_triggered,
            "dragon_event_defeated": self.dragon_event_defeated,
            "village_upgrade_free": self.village_upgrade_free,
            "village_orc_event_triggered": self.village_orc_event_triggered,
            "village_orcs_defeated": self.village_orcs_defeated,
            "moat_serpents_remaining": self.moat_serpents_remaining,
            "quest_metal_bought": self.quest_metal_bought,
            "quest_plant_collected": self.quest_plant_collected,
            "quest_plant_available": self.quest_plant_available,
            "deep_dungeon_cleared": self.deep_dungeon_cleared,
            "king_defeated": self.king_defeated,
            "crown_fixed": self.crown_fixed,
            "player_crowned": self.player_crowned
        }
        with open(self.save_file, "w") as f:
            json.dump(data, f)
        print("💾 Game saved!")

    def load_game(self):
        if not os.path.exists(self.save_file):
            print("No save file found.")
            return False
        with open(self.save_file, "r") as f:
            data = json.load(f)
        self.player = Character(data["name"], data["character_class"], data.get("gender", "male"))
        self.player.level = data["level"]
        self.player.max_health = data["max_health"]
        self.player.health = data["health"]
        self.player.experience = data["experience"]
        self.player.experience_to_next = data["experience_to_next"]
        self.player.strength = data["strength"]
        self.player.defense = data["defense"]
        self.player.magic = data["magic"]
        self.player.gold = data["gold"]
        self.player.inventory = data["inventory"]
        self.current_room = data["current_room"]
        self.room_enemy_state = data["room_enemy_state"]
        self.dragon_event_triggered = data["dragon_event_triggered"]
        self.dragon_event_defeated = data["dragon_event_defeated"]
        self.village_upgrade_free = data.get("village_upgrade_free", True)
        self.village_orc_event_triggered = data.get("village_orc_event_triggered", False)
        self.village_orcs_defeated = data.get("village_orcs_defeated", False)
        self.moat_serpents_remaining = data.get("moat_serpents_remaining", 2)
        self.quest_metal_bought = data.get("quest_metal_bought", False)
        self.quest_plant_collected = data.get("quest_plant_collected", False)
        self.quest_plant_available = data.get("quest_plant_available", False)
        self.deep_dungeon_cleared = data.get("deep_dungeon_cleared", False)
        self.king_defeated = data.get("king_defeated", False)
        self.crown_fixed = data.get("crown_fixed", False)
        self.player_crowned = data.get("player_crowned", False)
        print(f"🔄 Loaded save for {self.player.name} the {self.player.character_class} (Level {self.player.level})!")
        return True
    
    def village(self):
        # Called when in the village and player types 'upgrade'
        wooden_swords = [item for item in self.player.inventory if item == "Wooden Sword"]
        steel_swords = [item for item in self.player.inventory if item == "Steel Sword"]
        iron_swords = [item for item in self.player.inventory if item == "Iron Sword"]
        crystal_swords = [item for item in self.player.inventory if item == "Crystal Sword"]
        
        wooden_shields = [item for item in self.player.inventory if item == "Wooden Shield"]
        steel_shields = [item for item in self.player.inventory if item == "Steel Shield"]
        iron_shields = [item for item in self.player.inventory if item == "Iron Shield"]
        crystal_shields = [item for item in self.player.inventory if item == "Crystal Shield"]
        
        magic_staffs = [item for item in self.player.inventory if item == "Magic Staff"]
        enhanced_staffs = [item for item in self.player.inventory if item == "Enhanced Staff"]
        mythic_staffs = [item for item in self.player.inventory if item == "Mythic Staff"]
        
        print("Welcome to the blacksmith! What would you like to upgrade?")
        print("1. Sword")
        print("2. Shield") 
        print("3. Magic Staff")
        
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == "1":
            # Sword upgrade logic
            best_sword = None
            if wooden_swords:
                best_sword = "Wooden Sword"
            elif steel_swords:
                best_sword = "Steel Sword"
            elif iron_swords:
                best_sword = "Iron Sword"
            elif crystal_swords:
                best_sword = "Crystal Sword"
            
            if not best_sword:
                print("You don't have a sword to upgrade!")
                return
            
            next_tier = self.sword_tiers[best_sword]["next"]
            if not next_tier:
                print("Your sword is already at maximum level!")
                return
            
            upgrade_cost = self.sword_tiers[best_sword]["cost"]
            
            if self.village_upgrade_free and best_sword == "Wooden Sword":
                print("Your first sword upgrade is free!")
                self.player.inventory.remove(best_sword)
                self.player.inventory.append(next_tier)
                self.player.strength += self.sword_tiers[next_tier]["strength"]
                print(f"Your {best_sword} has been upgraded to a {next_tier}! Strength +{self.sword_tiers[next_tier]['strength']}!")
                self.village_upgrade_free = False
            else:
                print(f"Upgrading your {best_sword} to a {next_tier} costs {upgrade_cost} gold. Proceed? (y/n)")
                choice = input().strip().lower()
                if choice != "y":
                    print("Upgrade cancelled.")
                    return
                if self.player.gold < upgrade_cost:
                    print(f"You don't have enough gold! You need {upgrade_cost} gold.")
                    return
                self.player.gold -= upgrade_cost
                self.player.inventory.remove(best_sword)
                self.player.inventory.append(next_tier)
                self.player.strength += self.sword_tiers[next_tier]["strength"]
                print(f"Your {best_sword} has been upgraded to a {next_tier}! Strength +{self.sword_tiers[next_tier]['strength']}! (-{upgrade_cost} gold)")
        
        elif choice == "2":
            # Shield upgrade logic
            best_shield = None
            if wooden_shields:
                best_shield = "Wooden Shield"
            elif steel_shields:
                best_shield = "Steel Shield"
            elif iron_shields:
                best_shield = "Iron Shield"
            elif crystal_shields:
                best_shield = "Crystal Shield"
            
            if not best_shield:
                print("You don't have a shield to upgrade!")
                return
            
            next_tier = self.shield_tiers[best_shield]["next"]
            if not next_tier:
                print("Your shield is already at maximum level!")
                return
            
            upgrade_cost = self.shield_tiers[best_shield]["cost"]
            
            if best_shield == "Wooden Shield":
                if not self.quest_metal_bought:
                    print("To upgrade your shield, you need to buy some metal from the shop in town first!")
                    return
                print("Your first shield upgrade is free!")
                self.player.inventory.remove(best_shield)
                self.player.inventory.append(next_tier)
                self.player.defense += self.shield_tiers[next_tier]["defense"]
                print(f"Your {best_shield} has been upgraded to a {next_tier}! Defense +{self.shield_tiers[next_tier]['defense']}!")
            else:
                print(f"Upgrading your {best_shield} to a {next_tier} costs {upgrade_cost} gold. Proceed? (y/n)")
                choice = input().strip().lower()
                if choice != "y":
                    print("Upgrade cancelled.")
                    return
                if self.player.gold < upgrade_cost:
                    print(f"You don't have enough gold! You need {upgrade_cost} gold.")
                    return
                self.player.gold -= upgrade_cost
                self.player.inventory.remove(best_shield)
                self.player.inventory.append(next_tier)
                self.player.defense += self.shield_tiers[next_tier]["defense"]
                print(f"Your {best_shield} has been upgraded to a {next_tier}! Defense +{self.shield_tiers[next_tier]['defense']}! (-{upgrade_cost} gold)")
        
        elif choice == "3":
            # Magic staff upgrade logic
            best_staff = None
            if magic_staffs:
                best_staff = "Magic Staff"
            elif enhanced_staffs:
                best_staff = "Enhanced Staff"
            elif mythic_staffs:
                best_staff = "Mythic Staff"
            
            if not best_staff:
                print("You don't have a magic staff to upgrade!")
                return
            
            next_tier = self.staff_tiers[best_staff]["next"]
            if not next_tier:
                print("Your staff is already at maximum level!")
                return
            
            upgrade_cost = self.staff_tiers[best_staff]["cost"]
            
            if not self.quest_plant_collected:
                print("To upgrade your magic staff, I need a glowing plant from the cave. Go find one for me!")
                self.quest_plant_available = True
                return
            
            if upgrade_cost > 0:
                print(f"Upgrading your {best_staff} to a {next_tier} costs {upgrade_cost} gold. Proceed? (y/n)")
                choice = input().strip().lower()
                if choice != "y":
                    print("Upgrade cancelled.")
                    return
                if self.player.gold < upgrade_cost:
                    print(f"You don't have enough gold! You need {upgrade_cost} gold.")
                    return
                self.player.gold -= upgrade_cost
                print(f"(-{upgrade_cost} gold)")
            
            # Consume the plant and upgrade the staff
            self.player.inventory.remove("Glowing Plant")
            self.player.inventory.remove(best_staff)
            self.player.inventory.append(next_tier)
            self.player.magic += self.staff_tiers[best_staff]["magic"]
            print(f"Your {best_staff} has been upgraded to a {next_tier}! Magic +{self.staff_tiers[best_staff]['magic']}!")
            self.quest_plant_collected = False
            self.quest_plant_available = True  # Allow getting another plant
        
        else:
            print("Invalid choice.")
    
    def village_orc_battle(self):
        # Multi-orc battle event in the village
        orcs = [Enemy("Orc", 50, 12, 8, 40, 25) for _ in range(3)]
        self.emit("Three orcs are attacking the village!")
        while any(orc.is_alive() for orc in orcs) and self.player.is_alive():
            print(f"\nYour HP: {self.player.health}/{self.player.max_health}")
            for idx, orc in enumerate(orcs):
                status = f"Orc {idx+1}: {orc.health}/{orc.max_health} HP" if orc.is_alive() else f"Orc {idx+1}: Defeated"
                print(status)
            # Choose how many orcs to attack
            living_orcs = [i for i, orc in enumerate(orcs) if orc.is_alive()]
            print(f"How many orcs do you want to attack this round? (1-{len(living_orcs)})")
            while True:
                try:
                    num = int(input().strip())
                    if 1 <= num <= len(living_orcs):
                        break
                except:
                    pass
                print(f"Enter a number between 1 and {len(living_orcs)}.")
            # Choose attack type
            print("Choose your attack type:")
            print("1. Normal Attack")
            print("2. Magic Attack")
            has_axe = "Axe" in self.player.inventory
            if has_axe:
                print("3. Axe Attack 🪓")
            while True:
                atype = input("Enter your choice: ").strip()
                if atype in ["1", "2"] or (atype == "3" and has_axe):
                    break
                print("Invalid choice.")
            # Calculate base damage
            if atype == "1":
                base_damage = max(1, self.player.strength - orcs[0].defense)
            elif atype == "2":
                base_damage = max(1, self.player.magic - orcs[0].defense)
            elif atype == "3":
                base_damage = max(1, 20 + self.player.strength - orcs[0].defense)
            # Split damage based on number of targets
            damage_per_orc = base_damage // num
            # Attack selected orcs
            for i in living_orcs[:num]:
                orc = orcs[i]
                if not orc.is_alive():
                    continue
                orc.health -= damage_per_orc
                print(f"You attack Orc {i+1} for {damage_per_orc} damage!")
            # All living orcs attack back
            for i, orc in enumerate(orcs):
                if orc.is_alive():
                    damage = max(1, orc.strength - self.player.defense)
                    self.player.health -= damage
                    print(f"Orc {i+1} attacks you for {damage} damage!")
            if not self.player.is_alive():
                print("\nYou were defeated by the orcs!")
                return
        print("\nYou have defeated all the orcs and saved the village!")
        print("The bridge to the castle is now accessible!")
        self.village_orcs_defeated = True
        # Reward: gold and experience
        self.player.gold += 100
        self.player.gain_experience(120)
        print("You gained 100 gold and 120 experience!")
        # Activate staff upgrade quest
        if "Magic Staff" in self.player.inventory:
            print("The blacksmith mentions he could upgrade your magic staff if you bring him a glowing plant from the cave!")
            self.quest_plant_available = True
    
    def show_rooms(self):
        # Show all rooms and which have been visited, only show accessible rooms
        accessible = set(["town", "forest", "cave", "dungeon"])
        if self.player.level >= 6:
            accessible.update(["road", "village", "field"])
            if self.village_orcs_defeated:
                accessible.add("bridge")
                accessible.add("moat")
                accessible.add("castle_garden")
                accessible.add("dining_hall")
                accessible.add("throne_room")
        print("\n🌍 ROOMS IN THE GAME:")
        for room in [
            "town", "forest", "cave", "dungeon", "road", "village", "field", "bridge", "moat", "castle_garden", "dining_hall", "throne_room"
        ]:
            if room in accessible:
                status = "(visited)" if room in self.visited_rooms else "(not visited)"
                print(f"- {room.replace('_', ' ').title()} {status}")
        print()
    
    def game_loop(self):
        road_announced = False
        axe_announced = False
        throne_passage_open = False
        while self.game_running and self.player.is_alive():
            self.clear_screen()
            self.show_title()
            self.show_status()
            # Announce road access at level 6
            if self.player.level >= 6 and not road_announced:
                print("\n🛣️  You are now strong enough to travel the road! You can access the road from the town.")
                road_announced = True
                input("Press Enter to continue...")
            # Announce axe availability after dragon is defeated
            if self.dragon_event_defeated and not axe_announced:
                print("\n🪓 The Axe is now available in the shop in town!")
                axe_announced = True
                input("Press Enter to continue...")
            # Check for dragon event trigger
            if self.player.level >= 2 and not self.dragon_event_triggered and not self.dragon_event_defeated:
                self.dragon_event_triggered = True
                print("\n🔥 A mighty roar echoes across the land... A dragon is attacking the town! Return to town to defend it!")
                input("Press Enter to continue...")
            # Check for throne room secret passage
            if self.current_room == "throne_room" and hasattr(self, 'throne_riddle_shown') and not throne_passage_open:
                print("\n(Type 'enter' or 'enter passage' to go through the hidden passage, or any other command to stay.)")
                throne_passage_open = True
            # Trigger coronation ceremony if conditions are met
            if (
                self.current_room == "throne_room"
                and self.king_defeated
                and self.crown_fixed
                and not self.player_crowned
            ):
                self.coronation_ceremony()
            enemy, enemy_id = self.show_room()
            if enemy:
                if not self.combat(enemy, enemy_id):
                    continue
            command = input("\nWhat would you like to do? ").strip().lower().split()
            if not command:
                continue
            action = command[0]
            # Handle throne room secret passage
            if self.current_room == "throne_room" and throne_passage_open and action in ["enter", "enterpassage", "enter_passage", "passage"]:
                print("You step into the hidden passage...")
                self.current_room = "field"
                input("You find yourself in the field! Press Enter to continue...")
                continue
            # Handle take plant command
            if action == "take" and len(command) > 1 and command[1] == "plant":
                if self.current_room == "cave" and self.quest_plant_available and not self.quest_plant_collected:
                    self.player.inventory.append("Glowing Plant")
                    self.quest_plant_collected = True
                    print("You carefully pick the glowing plant and add it to your inventory.")
                else:
                    print("There's nothing to take here.")
                input("Press Enter to continue...")
                continue
            if action == "quit":
                print("Thanks for playing!")
                self.game_running = False
            elif action == "help":
                self.show_help()
                input("Press Enter to continue...")
            elif action == "status":
                self.show_status()
                input("Press Enter to continue...")
            elif action == "look":
                input("Press Enter to continue...")
            elif action == "move" and len(command) > 1:
                self.move(command[1])
            elif action == "shop" and self.current_room == "town":
                self.shop()
                input("Press Enter to continue...")
            elif action == "shop" and self.current_room != "town":
                print("❌ You can only shop in town!")
                input("Press Enter to continue...")
            elif action == "upgrade" and self.current_room == "village":
                self.village()
                input("Press Enter to continue...")
            elif action == "talk" and self.current_room == "village":
                self.blacksmith_talk()
                input("Press Enter to continue...")
            elif action == "rooms":
                self.show_rooms()
                input("Press Enter to continue...")
            elif action == "save":
                self.save_game()
                input("Press Enter to continue...")
            elif action == "load":
                self.load_game()
                input("Press Enter to continue...")
            else:
                print("❌ Unknown command. Type 'help' for available commands.")
                input("Press Enter to continue...")

    def garden_skeleton_battle(self):
        # Multi-skeleton battle in castle garden
        skeletons = [Enemy("Skeleton", 25, 10, 6, 30, 20) for _ in range(4)]
        print("Four skeletons are blocking your path!")
        while any(skeleton.is_alive() for skeleton in skeletons) and self.player.is_alive():
            print(f"\nYour HP: {self.player.health}/{self.player.max_health}")
            for idx, skeleton in enumerate(skeletons):
                status = f"Skeleton {idx+1}: {skeleton.health}/{skeleton.max_health} HP" if skeleton.is_alive() else f"Skeleton {idx+1}: Defeated"
                print(status)
            # Choose how many skeletons to attack
            living_skeletons = [i for i, skeleton in enumerate(skeletons) if skeleton.is_alive()]
            print(f"How many skeletons do you want to attack this round? (1-{len(living_skeletons)})")
            while True:
                try:
                    num = int(input().strip())
                    if 1 <= num <= len(living_skeletons):
                        break
                except:
                    pass
                print(f"Enter a number between 1 and {len(living_skeletons)}.")
            # Choose attack type
            print("Choose your attack type:")
            print("1. Normal Attack")
            print("2. Magic Attack")
            has_axe = "Axe" in self.player.inventory
            if has_axe:
                print("3. Axe Attack 🪓")
            while True:
                atype = input("Enter your choice: ").strip()
                if atype in ["1", "2"] or (atype == "3" and has_axe):
                    break
                print("Invalid choice.")
            # Calculate base damage
            if atype == "1":
                base_damage = max(1, self.player.strength - skeletons[0].defense)
            elif atype == "2":
                base_damage = max(1, self.player.magic - skeletons[0].defense)
            elif atype == "3":
                base_damage = max(1, 20 + self.player.strength - skeletons[0].defense)
            # Split damage based on number of targets
            damage_per_skeleton = base_damage // num
            # Attack selected skeletons
            for i in living_skeletons[:num]:
                skeleton = skeletons[i]
                if not skeleton.is_alive():
                    continue
                skeleton.health -= damage_per_skeleton
                print(f"You attack Skeleton {i+1} for {damage_per_skeleton} damage!")
            # All living skeletons attack back
            for i, skeleton in enumerate(skeletons):
                if skeleton.is_alive():
                    damage = max(1, skeleton.strength - self.player.defense)
                    self.player.health -= damage
                    print(f"Skeleton {i+1} attacks you for {damage} damage!")
            if not self.player.is_alive():
                print("\nYou were defeated by the skeletons!")
                return
        print("\nYou have defeated all the skeletons!")
        self.garden_cleared = True
        # Reward: gold and experience
        self.player.gold += 80
        self.player.gain_experience(100)
        print("You gained 80 gold and 100 experience!")

    def dining_orc_battle(self):
        # Multi-orc battle in dining hall
        orcs = [Enemy("Orc", 50, 12, 8, 40, 25) for _ in range(4)]
        print("Four orcs are feasting in the dining hall!")
        while any(orc.is_alive() for orc in orcs) and self.player.is_alive():
            print(f"\nYour HP: {self.player.health}/{self.player.max_health}")
            for idx, orc in enumerate(orcs):
                status = f"Orc {idx+1}: {orc.health}/{orc.max_health} HP" if orc.is_alive() else f"Orc {idx+1}: Defeated"
                print(status)
            # Choose how many orcs to attack
            living_orcs = [i for i, orc in enumerate(orcs) if orc.is_alive()]
            print(f"How many orcs do you want to attack this round? (1-{len(living_orcs)})")
            while True:
                try:
                    num = int(input().strip())
                    if 1 <= num <= len(living_orcs):
                        break
                except:
                    pass
                print(f"Enter a number between 1 and {len(living_orcs)}.")
            # Choose attack type
            print("Choose your attack type:")
            print("1. Normal Attack")
            print("2. Magic Attack")
            has_axe = "Axe" in self.player.inventory
            if has_axe:
                print("3. Axe Attack 🪓")
            while True:
                atype = input("Enter your choice: ").strip()
                if atype in ["1", "2"] or (atype == "3" and has_axe):
                    break
                print("Invalid choice.")
            # Calculate base damage
            if atype == "1":
                base_damage = max(1, self.player.strength - orcs[0].defense)
            elif atype == "2":
                base_damage = max(1, self.player.magic - orcs[0].defense)
            elif atype == "3":
                base_damage = max(1, 20 + self.player.strength - orcs[0].defense)
            # Split damage based on number of targets
            damage_per_orc = base_damage // num
            # Attack selected orcs
            for i in living_orcs[:num]:
                orc = orcs[i]
                if not orc.is_alive():
                    continue
                orc.health -= damage_per_orc
                print(f"You attack Orc {i+1} for {damage_per_orc} damage!")
            # All living orcs attack back
            for i, orc in enumerate(orcs):
                if orc.is_alive():
                    damage = max(1, orc.strength - self.player.defense)
                    self.player.health -= damage
                    print(f"Orc {i+1} attacks you for {damage} damage!")
            if not self.player.is_alive():
                print("\nYou were defeated by the orcs!")
                return
        print("\nYou have defeated all the orcs in the dining hall!")
        self.dining_cleared = True
        # Reward: gold and experience
        self.player.gold += 120
        self.player.gain_experience(150)
        print("You gained 120 gold and 150 experience!")

    def deep_dungeon_battle(self):
        # Epic multi-enemy battle in deep dungeon
        dragon = Enemy("Dragon", 120, 25, 18, 150, 200)
        troll = Enemy("Troll", 100, 20, 15, 120, 150)
        orc1 = Enemy("Orc", 60, 15, 10, 60, 50)
        orc2 = Enemy("Orc", 60, 15, 10, 60, 50)
        enemies = [dragon, troll, orc1, orc2]
        enemy_names = ["Dragon", "Troll", "Orc 1", "Orc 2"]
        
        print("The ultimate challenge awaits!")
        while any(enemy.is_alive() for enemy in enemies) and self.player.is_alive():
            print(f"\nYour HP: {self.player.health}/{self.player.max_health}")
            for idx, enemy in enumerate(enemies):
                status = f"{enemy_names[idx]}: {enemy.health}/{enemy.max_health} HP" if enemy.is_alive() else f"{enemy_names[idx]}: Defeated"
                print(status)
            # Choose how many enemies to attack
            living_enemies = [i for i, enemy in enumerate(enemies) if enemy.is_alive()]
            print(f"How many enemies do you want to attack this round? (1-{len(living_enemies)})")
            while True:
                try:
                    num = int(input().strip())
                    if 1 <= num <= len(living_enemies):
                        break
                except:
                    pass
                print(f"Enter a number between 1 and {len(living_enemies)}.")
            # Choose attack type
            print("Choose your attack type:")
            print("1. Normal Attack")
            print("2. Magic Attack")
            has_axe = "Axe" in self.player.inventory
            if has_axe:
                print("3. Axe Attack 🪓")
            while True:
                atype = input("Enter your choice: ").strip()
                if atype in ["1", "2"] or (atype == "3" and has_axe):
                    break
                print("Invalid choice.")
            # Calculate base damage
            if atype == "1":
                base_damage = max(1, self.player.strength - enemies[0].defense)
            elif atype == "2":
                base_damage = max(1, self.player.magic - enemies[0].defense)
            elif atype == "3":
                base_damage = max(1, 20 + self.player.strength - enemies[0].defense)
            # Split damage based on number of targets
            damage_per_enemy = base_damage // num
            # Attack selected enemies
            for i in living_enemies[:num]:
                enemy = enemies[i]
                if not enemy.is_alive():
                    continue
                enemy.health -= damage_per_enemy
                print(f"You attack {enemy_names[i]} for {damage_per_enemy} damage!")
            # All living enemies attack back
            for i, enemy in enumerate(enemies):
                if enemy.is_alive():
                    damage = max(1, enemy.strength - self.player.defense)
                    self.player.health -= damage
                    print(f"{enemy_names[i]} attacks you for {damage} damage!")
            if not self.player.is_alive():
                print("\nYou were defeated in the deep dungeon!")
                return
        print("\n🎉 You have defeated all the enemies in the deep dungeon!")
        print("You find a chest in the corner...")
        input("Press Enter to open it...")
        print("Inside you find the King's Crown!")
        self.player.inventory.append("King's Crown")
        self.player.strength += 20
        self.player.defense += 20
        self.player.magic += 20
        print("The crown grants you incredible power!")
        print("Strength +20, Defense +20, Magic +20!")
        self.deep_dungeon_cleared = True
        # Reward: gold and experience
        self.player.gold += 500
        self.player.gain_experience(300)
        print("You gained 500 gold and 300 experience!")

    def king_battle(self):
        # Final boss battle against the King
        king_health = self.player.max_health * 2
        king = Enemy("King", king_health, 50, 25, 500, 1000)  # Increased strength from 30 to 50
        
        print(f"\n🔥🔥🔥 THE FINAL BATTLE 🔥🔥🔥")
        print("The King's form begins to twist and contort...")
        print("His skin darkens, his eyes glow red, and horns sprout from his head!")
        print("'FOOLISH MORTAL!' the demon king roars.")
        print("'You thought this was a simple quest to restore the castle?'")
        print("'I lured you here with the promise of treasure and glory!'")
        print("'The crown you found was a trap - it was meant to weaken you!'")
        print("'Now you will face the true ruler of this realm!'")
        print(f"\nThe Demon King has {king_health} health points!")
        print("This is the final battle for the fate of the kingdom!")
        
        while king.is_alive() and self.player.is_alive():
            print(f"\nYour HP: {self.player.health}/{self.player.max_health}")
            print(f"Demon King: {king.health}/{king.max_health} HP")
            
            print("\nChoose your action:")
            print("1. Attack")
            print("2. Use Magic")
            print("3. Try to flee")
            has_axe = "Axe" in self.player.inventory
            if has_axe:
                print("4. Attack with Axe 🪓")
            
            choice = input("Enter your choice (1-3" + ("/4" if has_axe else "") + "): ").strip()
            
            if choice == "1":
                damage = max(1, self.player.strength - king.defense)
                king.health -= damage
                print(f"💥 You attack for {damage} damage!")
            elif choice == "2":
                damage = max(1, self.player.magic - king.defense)
                king.health -= damage
                print(f"🔮 You cast a spell for {damage} damage!")
            elif choice == "3":
                print("You cannot flee from the final battle!")
                continue
            elif choice == "4" and has_axe:
                damage = max(1, 20 + self.player.strength - king.defense)
                king.health -= damage
                print(f"🪓 You swing the Axe for {damage} massive damage!")
            else:
                print("❌ Invalid choice.")
                continue
            
            if king.is_alive():
                damage = max(1, king.strength - self.player.defense)
                self.player.health -= damage
                print(f"👑 The Demon King attacks for {damage} damage!")
        
        if self.player.is_alive():
            print("\n🏆 YOU HAVE DEFEATED THE DEMON KING!")
            print("The demon's form crumbles to dust...")
            print("A wave of pure light washes over the castle...")
            print("The castle is restored to its former glory!")
            print("All the evil creatures that haunted it have been banished!")
            self.king_defeated = True
            # Clear castle enemies permanently - including single enemies
            self.room_enemy_state["castle_garden"] = []
            self.room_enemy_state["dining_hall"] = []
            self.room_enemy_state["throne_room"] = []
            # Final rewards
            self.player.gold += 1000
            self.player.gain_experience(500)
            print("You gained 1000 gold and 500 experience!")
            print("Congratulations! You have completed the adventure!")
        else:
            print(f"\n💀 You were defeated by the Demon King!")
            return

    def coronation_ceremony(self):
        """Coronation ceremony for the player"""
        royal_title = "King" if self.player.gender == "male" else "Queen"
        print("\n🎉🎉🎉 CORONATION CEREMONY 🎉🎉🎉")
        print("The blacksmith places the repaired crown upon your head...")
        print("A golden light surrounds you...")
        print("The castle itself seems to acknowledge your authority...")
        print(f"\n🏆 ALL HAIL {self.player.name.upper()}, THE NEW {royal_title.upper()} OF THE KINGDOM! 🏆")
        print(f"You are now the rightful {royal_title} of this realm!")
        self.player_crowned = True
        input("Press Enter to continue...")

    def blacksmith_talk(self):
        """Talk to the blacksmith after defeating the demon king"""
        if not self.king_defeated:
            print("The blacksmith is busy working on weapons and armor.")
            return
        
        if "King's Crown" in self.player.inventory and not self.crown_fixed:
            print("\nThe blacksmith looks up from his work and notices the crown in your inventory.")
            print("'My word! Is that the King's Crown?' he exclaims.")
            print("'I can see it's been damaged in battle. Let me fix it for you!'")
            print("'Once it's repaired, you can be properly crowned in the throne room.'")
            print("\nThe blacksmith carefully repairs the crown...")
            print("✅ The crown has been restored to its full glory!")
            self.crown_fixed = True
            print("You can now return to the throne room for your coronation ceremony!")
        elif self.crown_fixed and not self.player_crowned:
            print("'The crown is ready! Go to the throne room for your coronation ceremony.'")
        elif self.player_crowned:
            royal_title = "King" if self.player.gender == "male" else "Queen"
            print(f"'My {royal_title}! It's an honor to serve you.'")
            print("'The kingdom is safe under your rule.'")
        else:
            print("The blacksmith is busy working on weapons and armor.")

def main():
    """Main function"""
    game = Game()
    # Offer to load game if save file exists
    if os.path.exists(game.save_file):
        print("A save file was found.")
        choice = input("Do you want to load your previous game? (y/n): ").strip().lower()
        if choice == "y":
            if game.load_game():
                game.game_loop()
                return
    game.create_character()
    game.game_loop()

if __name__ == "__main__":
    main() 