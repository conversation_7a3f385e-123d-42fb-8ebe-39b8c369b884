<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beetle Adventure - 3D RPG</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #87CEEB;
            overflow: hidden;
            font-family: 'Courier New', monospace;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 100;
        }
        
        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: white;
            box-shadow: 0 0 2px rgba(0,0,0,0.8);
        }
        
        #crosshair::before {
            width: 2px;
            height: 20px;
            left: 9px;
            top: 0;
        }
        
        #crosshair::after {
            width: 20px;
            height: 2px;
            left: 0;
            top: 9px;
        }
        
        #instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            font-size: 12px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #inventory {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #inventoryPanel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #8B4513;
            border: 4px solid #A0522D;
            border-style: outset;
            padding: 16px;
            z-index: 300;
            display: none;
            color: white;
            font-family: 'Courier New', monospace;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            box-shadow:
                inset 2px 2px 0px #D2B48C,
                inset -2px -2px 0px #654321,
                4px 4px 8px rgba(0,0,0,0.5);
        }

        #inventoryGrid {
            display: grid;
            grid-template-columns: repeat(5, 64px);
            grid-template-rows: repeat(4, 64px);
            gap: 2px;
            margin-bottom: 16px;
            background: #654321;
            padding: 8px;
            border: 2px solid #A0522D;
            border-style: inset;
        }

        .inventory-slot {
            width: 64px;
            height: 64px;
            border: 2px solid #A0522D;
            border-style: inset;
            background: #8B4513;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .inventory-slot:hover {
            background: #A0522D;
            border-color: #D2B48C;
        }

        .inventory-slot.selected {
            border: 2px solid #ffff00;
            border-style: outset;
            background: #B8860B;
            box-shadow:
                inset 1px 1px 0px #ffff88,
                inset -1px -1px 0px #888800;
        }

        .inventory-slot.has-item {
            background: #A0522D;
            border-style: outset;
        }

        .item-icon {
            font-size: 32px;
            text-shadow: 2px 2px 0px rgba(0,0,0,1);
            image-rendering: pixelated;
            filter: contrast(1.2) saturate(1.1);
        }

        .item-count {
            position: absolute;
            bottom: 4px;
            right: 6px;
            font-size: 12px;
            color: white;
            text-shadow: 1px 1px 0px rgba(0,0,0,1);
            font-weight: bold;
            background: rgba(0,0,0,0.7);
            padding: 1px 3px;
            border: 1px solid #666;
        }

        #inventoryInfo {
            text-align: center;
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 2px 2px 0px rgba(0,0,0,1);
        }

        #selectedItemInfo {
            min-height: 48px;
            padding: 12px;
            background: #654321;
            border: 2px solid #A0522D;
            border-style: inset;
            margin-bottom: 12px;
            font-size: 14px;
            box-shadow: inset 1px 1px 2px rgba(0,0,0,0.5);
        }

        .inventory-controls {
            text-align: center;
            font-size: 12px;
            color: #F5DEB3;
            text-shadow: 1px 1px 0px rgba(0,0,0,1);
            background: #8B4513;
            padding: 8px;
            border: 1px solid #A0522D;
            border-style: inset;
        }

        /* Minecraft-style pixelated item icons */
        .item-icon {
            width: 32px;
            height: 32px;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Sword icon - pixelated blade */
        .item-icon.sword {
            background: linear-gradient(
                to bottom,
                #C0C0C0 0%, #C0C0C0 20%,
                #A9A9A9 20%, #A9A9A9 80%,
                #8B4513 80%, #8B4513 100%
            );
            border: 1px solid #696969;
        }

        /* Grass block icon */
        .item-icon.grass_block {
            background: linear-gradient(
                to bottom,
                #228B22 0%, #228B22 25%,
                #8B4513 25%, #8B4513 100%
            );
            border: 1px solid #006400;
        }

        /* Dirt block icon */
        .item-icon.dirt_block {
            background: #8B4513;
            border: 1px solid #654321;
            position: relative;
        }
        .item-icon.dirt_block::before {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: #A0522D;
            top: 8px;
            left: 8px;
            box-shadow:
                8px 0px 0px #A0522D,
                16px 0px 0px #A0522D,
                0px 8px 0px #A0522D,
                8px 8px 0px #654321,
                16px 8px 0px #A0522D,
                0px 16px 0px #654321,
                8px 16px 0px #A0522D,
                16px 16px 0px #654321;
        }

        /* Stone block icon */
        .item-icon.stone_block {
            background: #808080;
            border: 1px solid #696969;
            position: relative;
        }
        .item-icon.stone_block::before {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: #A9A9A9;
            top: 6px;
            left: 6px;
            box-shadow:
                8px 0px 0px #696969,
                16px 0px 0px #A9A9A9,
                0px 8px 0px #696969,
                8px 8px 0px #A9A9A9,
                16px 8px 0px #696969,
                0px 16px 0px #A9A9A9,
                8px 16px 0px #696969,
                16px 16px 0px #A9A9A9;
        }

        /* Wood block icon */
        .item-icon.wood_block {
            background: #DEB887;
            border: 1px solid #8B7355;
            position: relative;
        }
        .item-icon.wood_block::before {
            content: '';
            position: absolute;
            width: 30px;
            height: 2px;
            background: #8B7355;
            top: 8px;
            left: 1px;
            box-shadow:
                0px 6px 0px #8B7355,
                0px 12px 0px #8B7355,
                0px 18px 0px #8B7355;
        }

        /* Health potion icon */
        .item-icon.health_potion {
            background: linear-gradient(
                to bottom,
                #FF69B4 0%, #FF69B4 20%,
                #FF1493 20%, #FF1493 80%,
                #8B4513 80%, #8B4513 100%
            );
            border: 1px solid #DC143C;
            border-radius: 4px;
        }

        /* Pickaxe icon */
        .item-icon.pickaxe {
            background: linear-gradient(
                45deg,
                #696969 0%, #696969 60%,
                #8B4513 60%, #8B4513 100%
            );
            border: 1px solid #2F4F4F;
        }

        /* Fist icon */
        .item-icon.fist {
            background: #DDBEA9;
            border: 1px solid #CB997E;
            border-radius: 2px;
        }

        /* Bow icon */
        .item-icon.bow {
            background: linear-gradient(
                to right,
                #8B4513 0%, #8B4513 20%,
                transparent 20%, transparent 80%,
                #8B4513 80%, #8B4513 100%
            );
            border: 1px solid #654321;
            position: relative;
        }
        .item-icon.bow::before {
            content: '';
            position: absolute;
            width: 2px;
            height: 24px;
            background: #D2B48C;
            left: 15px;
            top: 4px;
            border-radius: 50%;
        }

        /* Rendered 3D icons */
        .item-icon.rendered-icon {
            width: 56px !important;
            height: 56px !important;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            border: none;
            background: none;
            margin: 4px;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 200;
        }

        /* Character Creation Modal */
        #characterCreationModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Courier New', monospace;
        }

        .character-creation-panel {
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border: 4px solid #D2B48C;
            border-radius: 10px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            color: white;
            text-align: center;
            box-shadow: 0 0 20px rgba(0,0,0,0.8);
        }

        .character-creation-panel h1 {
            color: #FFD700;
            font-size: 28px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        .character-creation-panel h2 {
            color: #F5DEB3;
            font-size: 20px;
            margin: 20px 0 10px 0;
        }

        .character-creation-panel input[type="text"] {
            width: 80%;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #654321;
            border-radius: 5px;
            background: #F5DEB3;
            color: #654321;
            font-family: 'Courier New', monospace;
        }

        .character-creation-panel .class-selection,
        .character-creation-panel .gender-selection {
            display: flex;
            justify-content: space-around;
            margin: 15px 0;
        }

        .character-creation-panel .class-option,
        .character-creation-panel .gender-option {
            background: #654321;
            border: 2px solid #8B4513;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .character-creation-panel .class-option:hover,
        .character-creation-panel .gender-option:hover {
            background: #8B4513;
            border-color: #D2B48C;
            transform: scale(1.05);
        }

        .character-creation-panel .class-option.selected,
        .character-creation-panel .gender-option.selected {
            background: #B8860B;
            border-color: #FFD700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .character-creation-panel .start-button {
            background: #228B22;
            border: 3px solid #32CD32;
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            transition: all 0.3s;
        }

        .character-creation-panel .start-button:hover {
            background: #32CD32;
            transform: scale(1.05);
        }

        .character-creation-panel .start-button:disabled {
            background: #666;
            border-color: #999;
            cursor: not-allowed;
            transform: none;
        }

        /* Story UI Panel */
        #storyPanel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: rgba(139, 69, 19, 0.95);
            border: 3px solid #D2B48C;
            border-radius: 8px;
            padding: 15px;
            color: white;
            font-family: 'Courier New', monospace;
            z-index: 150;
            display: none;
        }

        #storyPanel h3 {
            color: #FFD700;
            margin: 0 0 10px 0;
            text-align: center;
            font-size: 18px;
        }

        #storyPanel .character-info {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }

        #storyPanel .character-info div {
            margin: 5px 0;
            font-size: 14px;
        }

        .story-toggle {
            position: fixed;
            top: 10px;
            right: 320px;
            background: #8B4513;
            border: 2px solid #D2B48C;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            z-index: 160;
            display: none;
        }

        .story-toggle:hover {
            background: #A0522D;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- Character Creation Modal -->
        <div id="characterCreationModal">
            <div class="character-creation-panel">
                <h1>🐛 BEETLE ADVENTURE 🐛</h1>
                <h2>Create Your Character</h2>

                <div style="margin: 20px 0;">
                    <label for="characterName" style="display: block; margin-bottom: 10px;">Character Name:</label>
                    <input type="text" id="characterName" placeholder="Enter your name..." maxlength="20">
                </div>

                <h2>Choose Your Class</h2>
                <div class="class-selection">
                    <div class="class-option" data-class="Warrior">
                        <div>⚔️ Warrior</div>
                        <small>High Strength & Defense</small>
                    </div>
                    <div class="class-option" data-class="Mage">
                        <div>🔮 Mage</div>
                        <small>High Magic Power</small>
                    </div>
                    <div class="class-option" data-class="Rogue">
                        <div>🗡️ Rogue</div>
                        <small>Balanced Stats</small>
                    </div>
                </div>

                <h2>Choose Your Gender</h2>
                <div class="gender-selection">
                    <div class="gender-option" data-gender="male">
                        <div>👨 Male</div>
                    </div>
                    <div class="gender-option" data-gender="female">
                        <div>👩 Female</div>
                    </div>
                </div>

                <button class="start-button" id="startAdventure" disabled>Start Adventure!</button>
            </div>
        </div>

        <!-- Story Panel Toggle Button -->
        <button class="story-toggle" id="storyToggle">📖 Story</button>

        <!-- Story Panel -->
        <div id="storyPanel">
            <h3>Character Info</h3>
            <div class="character-info">
                <div id="charName">Name: -</div>
                <div id="charClass">Class: -</div>
                <div id="charLevel">Level: 1</div>
                <div id="charExp">Experience: 0/100</div>
                <div id="charGold">Gold: 150</div>
            </div>
            <div class="character-info">
                <div id="charStrength">⚔️ Strength: -</div>
                <div id="charDefense">🛡️ Defense: -</div>
                <div id="charMagic">🔮 Magic: -</div>
            </div>
        </div>

        <div id="loading" style="display: none;">Loading Adventure World...</div>
        
        <div id="ui" style="display: none;">
            <div>❤️ Health: <span id="health">100</span>/100</div>
            <div style="display: flex; align-items: center; margin: 5px 0;">
                <span style="margin-right: 10px;">⚡ Stamina:</span>
                <div id="staminaBarContainer" style="width: 100px; height: 12px; background: rgba(0,0,0,0.5); border: 1px solid #666; border-radius: 2px; position: relative;">
                    <div id="staminaBar" style="width: 100%; height: 100%; background: linear-gradient(to right, #00ff00, #ffff00, #ff0000); border-radius: 1px; transition: width 0.1s ease;"></div>
                </div>
                <span id="staminaText" style="margin-left: 8px; font-size: 12px;">100</span>
            </div>
            <div>🏔️ Location: <span id="biome">Starting Area</span></div>
            <div>🧭 Town: <span id="townDirection">→ 100m East</span></div>
            <div>⏱️ FPS: <span id="fps">0</span></div>
            <div>🗡️ Orcs: <span id="orcs">0</span></div>
            <div>⏰ Time: <span id="time">12:00</span></div>
        </div>

        <div id="inventory" style="display: none;">
            <div>🗡️ Weapon: <span id="currentWeapon">Fist</span></div>
            <div>⚔️ Swords: <span id="swordCount">0</span></div>
            <div>💀 Kills: <span id="killCount">0</span></div>
            <div>🏆 Score: <span id="score">0</span></div>
        </div>
        
        <div id="crosshair" style="display: none;"></div>
        
        <div id="instructions" style="display: none;">
            WASD: Move | R: Run | Mouse: Look | Space: Jump | Left Click: Attack | I: Inventory | Mouse to lock
        </div>

        <!-- New Inventory Panel -->
        <div id="inventoryPanel">
            <div id="inventoryInfo">
                <h3>📦 Inventory</h3>
            </div>
            <div id="selectedItemInfo">
                <div id="selectedItemName">Select an item to see details</div>
                <div id="selectedItemDesc"></div>
            </div>
            <div id="inventoryGrid">
                <!-- Grid slots will be generated by JavaScript -->
            </div>
            <div class="inventory-controls">
                Left Click: Select/Use Item | Right Click: Drop Item | I or ESC: Close
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // Game constants - optimized for performance
        const CHUNK_SIZE = 16;
        const WORLD_HEIGHT = 24;
        const SEA_LEVEL = 12;
        const RENDER_DISTANCE = 3;

        // RPG Game State Management
        class GameState {
            constructor() {
                this.character = null;
                this.isCharacterCreated = false;
                this.currentRoom = "town";
                this.gameStarted = false;
            }

            createCharacter(name, characterClass, gender) {
                // Set base stats based on class (from game.py)
                let stats = { strength: 10, defense: 8, magic: 8 };

                if (characterClass === "Warrior") {
                    stats = { strength: 15, defense: 10, magic: 5 };
                } else if (characterClass === "Mage") {
                    stats = { strength: 8, defense: 6, magic: 15 };
                } else if (characterClass === "Rogue") {
                    stats = { strength: 12, defense: 8, magic: 8 };
                }

                this.character = {
                    name: name,
                    characterClass: characterClass,
                    gender: gender,
                    level: 1,
                    maxHealth: 100,
                    health: 100,
                    experience: 0,
                    experienceToNext: 100,
                    gold: 150,
                    inventory: [],
                    ...stats
                };

                this.isCharacterCreated = true;
                this.updateCharacterUI();
            }

            updateCharacterUI() {
                if (!this.character) return;

                document.getElementById('charName').textContent = `Name: ${this.character.name}`;
                document.getElementById('charClass').textContent = `Class: ${this.character.characterClass}`;
                document.getElementById('charLevel').textContent = `Level: ${this.character.level}`;
                document.getElementById('charExp').textContent = `Experience: ${this.character.experience}/${this.character.experienceToNext}`;
                document.getElementById('charGold').textContent = `Gold: ${this.character.gold}`;

                // Get equipment bonuses if player exists
                let equipmentBonuses = { strength: 0, defense: 0, magic: 0 };
                if (window.player && player.inventory) {
                    equipmentBonuses = player.inventory.getEquipmentBonuses();
                }

                // Show base stats + equipment bonuses
                const totalStrength = this.character.strength + equipmentBonuses.strength;
                const totalDefense = this.character.defense + equipmentBonuses.defense;
                const totalMagic = this.character.magic + equipmentBonuses.magic;

                document.getElementById('charStrength').textContent = `⚔️ Strength: ${totalStrength}${equipmentBonuses.strength > 0 ? ` (${this.character.strength}+${equipmentBonuses.strength})` : ''}`;
                document.getElementById('charDefense').textContent = `🛡️ Defense: ${totalDefense}${equipmentBonuses.defense > 0 ? ` (${this.character.defense}+${equipmentBonuses.defense})` : ''}`;
                document.getElementById('charMagic').textContent = `🔮 Magic: ${totalMagic}${equipmentBonuses.magic > 0 ? ` (${this.character.magic}+${equipmentBonuses.magic})` : ''}`;

                // Update main health display to use character health
                document.getElementById('health').textContent = this.character.health;
            }

            gainExperience(amount) {
                if (!this.character) return;

                this.character.experience += amount;
                if (this.character.experience >= this.character.experienceToNext) {
                    this.levelUp();
                }
                this.updateCharacterUI();
            }

            levelUp() {
                const oldLevel = this.character.level;
                this.character.level += 1;
                this.character.maxHealth += 20;
                this.character.health = this.character.maxHealth; // Full heal on level up
                this.character.strength += 2;
                this.character.defense += 2;
                this.character.magic += 2;
                this.character.experienceToNext = this.character.level * 100;

                // Show level up notification
                this.showLevelUpNotification(oldLevel, this.character.level);
                console.log(`🎉 ${this.character.name} leveled up to level ${this.character.level}!`);
            }

            showLevelUpNotification(oldLevel, newLevel) {
                // Create level up notification
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #FFD700, #FFA500);
                    border: 4px solid #FF8C00;
                    border-radius: 15px;
                    padding: 30px;
                    color: #8B4513;
                    font-family: 'Courier New', monospace;
                    font-size: 24px;
                    font-weight: bold;
                    text-align: center;
                    z-index: 2000;
                    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
                    animation: levelUpPulse 2s ease-in-out;
                `;

                notification.innerHTML = `
                    <div style="font-size: 32px; margin-bottom: 15px;">🎉 LEVEL UP! 🎉</div>
                    <div>${this.character.name} reached Level ${newLevel}!</div>
                    <div style="font-size: 18px; margin-top: 15px; color: #654321;">
                        Health: ${this.character.maxHealth} | Strength: ${this.character.strength}<br>
                        Defense: ${this.character.defense} | Magic: ${this.character.magic}
                    </div>
                `;

                // Add CSS animation
                if (!document.getElementById('levelUpStyle')) {
                    const style = document.createElement('style');
                    style.id = 'levelUpStyle';
                    style.textContent = `
                        @keyframes levelUpPulse {
                            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
                            50% { transform: translate(-50%, -50%) scale(1.1); opacity: 1; }
                            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
                        }
                    `;
                    document.head.appendChild(style);
                }

                document.body.appendChild(notification);

                // Remove notification after 4 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 4000);
            }
        }

        // Initialize game state
        const gameState = new GameState();

        // Initialize Three.js
        let scene, camera, renderer, world;
        let player;
        let frameCount = 0;
        let lastTime = 0;
        let gameTime = 0.5;
        
        // Simple noise implementation
        class NoiseGenerator {
            constructor(seed = 12345) {
                this.seed = seed;
            }
            
            hash(x, y) {
                let h = (x * 374761393 + y * 668265263) ^ this.seed;
                h = (h ^ (h >>> 13)) * 1274126177;
                return (h ^ (h >>> 16)) / 4294967296 + 0.5;
            }
            
            noise(x, y) {
                const ix = Math.floor(x);
                const iy = Math.floor(y);
                const fx = x - ix;
                const fy = y - iy;
                
                const a = this.hash(ix, iy);
                const b = this.hash(ix + 1, iy);
                const c = this.hash(ix, iy + 1);
                const d = this.hash(ix + 1, iy + 1);
                
                const i1 = a + (b - a) * fx;
                const i2 = c + (d - c) * fx;
                
                return i1 + (i2 - i1) * fy;
            }
            
            octaveNoise(x, y, octaves = 4) {
                let value = 0;
                let amplitude = 1;
                let frequency = 1;
                let maxValue = 0;
                
                for (let i = 0; i < octaves; i++) {
                    value += this.noise(x * frequency, y * frequency) * amplitude;
                    maxValue += amplitude;
                    amplitude *= 0.5;
                    frequency *= 2;
                }
                
                return value / maxValue;
            }
        }
        
        const noise = new NoiseGenerator();

        // Item icon renderer for 3D items
        class ItemIconRenderer {
            constructor() {
                this.iconCache = new Map();
                this.iconScene = new THREE.Scene();
                this.iconCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0.1, 100);
                this.iconRenderer = new THREE.WebGLRenderer({
                    antialias: false,
                    alpha: true,
                    preserveDrawingBuffer: true
                });
                this.iconRenderer.setSize(64, 64);
                this.iconRenderer.setClearColor(0x000000, 0);

                // Setup lighting for icons
                const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
                this.iconScene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
                directionalLight.position.set(1.5, 1.5, 1.5);
                this.iconScene.add(directionalLight);

                this.iconCamera.position.set(1.5, 1.5, 1.5);
                this.iconCamera.lookAt(0, 0, 0);
            }

            createSwordIcon() {
                const group = new THREE.Group();

                // Blade - much larger
                const bladeGeometry = new THREE.BoxGeometry(0.15, 1.2, 0.08);
                const bladeMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 });
                const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
                blade.position.y = 0.2;
                group.add(blade);

                // Handle - larger
                const handleGeometry = new THREE.BoxGeometry(0.2, 0.5, 0.15);
                const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                const handle = new THREE.Mesh(handleGeometry, handleMaterial);
                handle.position.y = -0.4;
                group.add(handle);

                // Guard - larger
                const guardGeometry = new THREE.BoxGeometry(0.5, 0.12, 0.12);
                const guardMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
                const guard = new THREE.Mesh(guardGeometry, guardMaterial);
                guard.position.y = -0.05;
                group.add(guard);

                // Scale the entire sword up
                group.scale.set(1.2, 1.2, 1.2);

                return group;
            }

            createPotionIcon() {
                const group = new THREE.Group();

                // Bottle body - much larger
                const bottleGeometry = new THREE.CylinderGeometry(0.4, 0.35, 1.0, 8);
                const bottleMaterial = new THREE.MeshLambertMaterial({
                    color: 0x8B4513,
                    transparent: true,
                    opacity: 0.7
                });
                const bottle = new THREE.Mesh(bottleGeometry, bottleMaterial);
                group.add(bottle);

                // Potion liquid - larger
                const liquidGeometry = new THREE.CylinderGeometry(0.35, 0.3, 0.75, 8);
                const liquidMaterial = new THREE.MeshLambertMaterial({
                    color: 0xFF1493,
                    transparent: true,
                    opacity: 0.9
                });
                const liquid = new THREE.Mesh(liquidGeometry, liquidMaterial);
                liquid.position.y = -0.05;
                group.add(liquid);

                // Cork - larger
                const corkGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.25, 8);
                const corkMaterial = new THREE.MeshLambertMaterial({ color: 0xD2B48C });
                const cork = new THREE.Mesh(corkGeometry, corkMaterial);
                cork.position.y = 0.6;
                group.add(cork);

                // Scale the entire potion up
                group.scale.set(1.3, 1.3, 1.3);

                return group;
            }

            createPickaxeIcon() {
                const group = new THREE.Group();

                // Handle - larger
                const handleGeometry = new THREE.BoxGeometry(0.12, 1.0, 0.12);
                const handleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                const handle = new THREE.Mesh(handleGeometry, handleMaterial);
                group.add(handle);

                // Pickaxe head - larger
                const headGeometry = new THREE.BoxGeometry(1.0, 0.2, 0.2);
                const headMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
                const head = new THREE.Mesh(headGeometry, headMaterial);
                head.position.y = 0.3;
                group.add(head);

                // Pick points - larger
                const pointGeometry = new THREE.ConeGeometry(0.08, 0.25, 4);
                const pointMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F4F });

                const leftPoint = new THREE.Mesh(pointGeometry, pointMaterial);
                leftPoint.position.set(-0.55, 0.3, 0);
                leftPoint.rotation.z = Math.PI / 2;
                group.add(leftPoint);

                const rightPoint = new THREE.Mesh(pointGeometry, pointMaterial);
                rightPoint.position.set(0.55, 0.3, 0);
                rightPoint.rotation.z = -Math.PI / 2;
                group.add(rightPoint);

                // Scale the entire pickaxe up
                group.scale.set(1.4, 1.4, 1.4);

                return group;
            }

            createGrassBlockIcon() {
                const group = new THREE.Group();

                // Create a cube with different materials for each face
                const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);

                // Materials for each face
                const materials = [
                    new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // right - dirt
                    new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // left - dirt
                    new THREE.MeshLambertMaterial({ color: 0x228B22 }), // top - grass
                    new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // bottom - dirt
                    new THREE.MeshLambertMaterial({ color: 0x8B4513 }), // front - dirt
                    new THREE.MeshLambertMaterial({ color: 0x8B4513 })  // back - dirt
                ];

                const cube = new THREE.Mesh(geometry, materials);
                group.add(cube);

                // Add some grass texture on top
                const grassGeometry = new THREE.PlaneGeometry(0.8, 0.8);
                const grassMaterial = new THREE.MeshLambertMaterial({
                    color: 0x32CD32,
                    transparent: true,
                    opacity: 0.8
                });
                const grassOverlay = new THREE.Mesh(grassGeometry, grassMaterial);
                grassOverlay.rotation.x = -Math.PI / 2;
                grassOverlay.position.y = 0.401;
                group.add(grassOverlay);

                group.scale.set(1.2, 1.2, 1.2);
                return group;
            }

            createDirtBlockIcon() {
                const group = new THREE.Group();

                const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
                const material = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                const cube = new THREE.Mesh(geometry, material);
                group.add(cube);

                // Add dirt texture spots
                const spotGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
                const spotMaterial = new THREE.MeshLambertMaterial({ color: 0xA0522D });

                // Add random dirt spots
                for (let i = 0; i < 8; i++) {
                    const spot = new THREE.Mesh(spotGeometry, spotMaterial);
                    spot.position.set(
                        (Math.random() - 0.5) * 0.6,
                        (Math.random() - 0.5) * 0.6,
                        (Math.random() - 0.5) * 0.6
                    );
                    group.add(spot);
                }

                group.scale.set(1.2, 1.2, 1.2);
                return group;
            }

            createStoneBlockIcon() {
                const group = new THREE.Group();

                const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
                const material = new THREE.MeshLambertMaterial({ color: 0x808080 });
                const cube = new THREE.Mesh(geometry, material);
                group.add(cube);

                // Add stone texture variations
                const lightSpotGeometry = new THREE.BoxGeometry(0.08, 0.08, 0.08);
                const lightSpotMaterial = new THREE.MeshLambertMaterial({ color: 0xA9A9A9 });
                const darkSpotMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });

                // Add random stone spots
                for (let i = 0; i < 12; i++) {
                    const spot = new THREE.Mesh(lightSpotGeometry, i % 2 ? lightSpotMaterial : darkSpotMaterial);
                    spot.position.set(
                        (Math.random() - 0.5) * 0.7,
                        (Math.random() - 0.5) * 0.7,
                        (Math.random() - 0.5) * 0.7
                    );
                    group.add(spot);
                }

                group.scale.set(1.2, 1.2, 1.2);
                return group;
            }

            createWoodBlockIcon() {
                const group = new THREE.Group();

                const geometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
                const material = new THREE.MeshLambertMaterial({ color: 0xDEB887 });
                const cube = new THREE.Mesh(geometry, material);
                group.add(cube);

                // Add wood grain lines
                const grainGeometry = new THREE.BoxGeometry(0.82, 0.02, 0.82);
                const grainMaterial = new THREE.MeshLambertMaterial({ color: 0x8B7355 });

                // Add horizontal grain lines
                for (let i = 0; i < 6; i++) {
                    const grain = new THREE.Mesh(grainGeometry, grainMaterial);
                    grain.position.y = -0.3 + (i * 0.12);
                    group.add(grain);
                }

                group.scale.set(1.2, 1.2, 1.2);
                return group;
            }

            createFistIcon() {
                const group = new THREE.Group();

                // Create a simple fist shape
                const palmGeometry = new THREE.BoxGeometry(0.4, 0.6, 0.3);
                const skinMaterial = new THREE.MeshLambertMaterial({ color: 0xDDBEA9 });
                const palm = new THREE.Mesh(palmGeometry, skinMaterial);
                group.add(palm);

                // Add fingers
                const fingerGeometry = new THREE.BoxGeometry(0.08, 0.3, 0.15);
                for (let i = 0; i < 4; i++) {
                    const finger = new THREE.Mesh(fingerGeometry, skinMaterial);
                    finger.position.set(-0.15 + (i * 0.1), 0.35, 0);
                    group.add(finger);
                }

                // Add thumb
                const thumbGeometry = new THREE.BoxGeometry(0.1, 0.25, 0.12);
                const thumb = new THREE.Mesh(thumbGeometry, skinMaterial);
                thumb.position.set(0.25, 0.1, 0);
                group.add(thumb);

                group.scale.set(1.3, 1.3, 1.3);
                return group;
            }

            createBowIcon() {
                const group = new THREE.Group();

                // Bow body (curved)
                const bowGeometry = new THREE.TorusGeometry(0.4, 0.05, 4, 8, Math.PI);
                const bowMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                const bow = new THREE.Mesh(bowGeometry, bowMaterial);
                bow.rotation.z = Math.PI / 2;
                group.add(bow);

                // Bow string
                const stringGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.7, 4);
                const stringMaterial = new THREE.MeshLambertMaterial({ color: 0xF5DEB3 });
                const string = new THREE.Mesh(stringGeometry, stringMaterial);
                string.position.x = 0.35;
                group.add(string);

                group.scale.set(1.4, 1.4, 1.4);
                return group;
            }

            renderIcon(itemId) {
                if (this.iconCache.has(itemId)) {
                    return this.iconCache.get(itemId);
                }

                // Clear scene
                while(this.iconScene.children.length > 2) { // Keep lights
                    this.iconScene.remove(this.iconScene.children[2]);
                }

                let itemMesh;
                switch(itemId) {
                    case 'sword':
                        itemMesh = this.createSwordIcon();
                        break;
                    case 'health_potion':
                        itemMesh = this.createPotionIcon();
                        break;
                    case 'pickaxe':
                        itemMesh = this.createPickaxeIcon();
                        break;
                    case 'grass_block':
                        itemMesh = this.createGrassBlockIcon();
                        break;
                    case 'dirt_block':
                        itemMesh = this.createDirtBlockIcon();
                        break;
                    case 'stone_block':
                        itemMesh = this.createStoneBlockIcon();
                        break;
                    case 'wood_block':
                        itemMesh = this.createWoodBlockIcon();
                        break;
                    case 'fist':
                        itemMesh = this.createFistIcon();
                        break;
                    case 'bow':
                        itemMesh = this.createBowIcon();
                        break;
                    default:
                        return null;
                }

                this.iconScene.add(itemMesh);
                this.iconRenderer.render(this.iconScene, this.iconCamera);

                // Get the rendered image as data URL
                const canvas = this.iconRenderer.domElement;
                const dataURL = canvas.toDataURL();

                this.iconCache.set(itemId, dataURL);
                return dataURL;
            }
        }

        let itemIconRenderer;

        // Block types
        const BLOCKS = {
            AIR: 0,
            GRASS: 1,
            DIRT: 2,
            STONE: 3,
            SAND: 4,
            SNOW: 5,
            WATER: 6,
            WOOD: 7,
            LEAVES: 8
        };

        // Item types and definitions
        const ITEM_TYPES = {
            WEAPON: 'weapon',
            BLOCK: 'block',
            TOOL: 'tool',
            CONSUMABLE: 'consumable'
        };

        const ITEMS = {
            // Weapons
            FIST: {
                id: 'fist',
                name: 'Fist',
                type: ITEM_TYPES.WEAPON,
                iconClass: 'fist',
                damage: 25,
                description: 'Your bare hands. Better than nothing!'
            },
            SWORD: {
                id: 'sword',
                name: 'Iron Sword',
                type: ITEM_TYPES.WEAPON,
                iconClass: 'sword',
                damage: 50,
                description: 'A sharp iron sword. Deals good damage to enemies.'
            },
            BOW: {
                id: 'bow',
                name: 'Wooden Bow',
                type: ITEM_TYPES.WEAPON,
                iconClass: 'bow',
                damage: 40,
                description: 'A ranged weapon. Good for keeping distance.'
            },
            // Blocks
            GRASS_BLOCK: {
                id: 'grass_block',
                name: 'Grass Block',
                type: ITEM_TYPES.BLOCK,
                iconClass: 'grass_block',
                blockType: BLOCKS.GRASS,
                description: 'A grassy dirt block. Can be placed in the world.'
            },
            DIRT_BLOCK: {
                id: 'dirt_block',
                name: 'Dirt Block',
                type: ITEM_TYPES.BLOCK,
                iconClass: 'dirt_block',
                blockType: BLOCKS.DIRT,
                description: 'Basic dirt block. Foundation for building.'
            },
            STONE_BLOCK: {
                id: 'stone_block',
                name: 'Stone Block',
                type: ITEM_TYPES.BLOCK,
                iconClass: 'stone_block',
                blockType: BLOCKS.STONE,
                description: 'Solid stone block. Durable building material.'
            },
            WOOD_BLOCK: {
                id: 'wood_block',
                name: 'Wood Block',
                type: ITEM_TYPES.BLOCK,
                iconClass: 'wood_block',
                blockType: BLOCKS.WOOD,
                description: 'Wooden plank block. Good for construction.'
            },
            // Tools
            PICKAXE: {
                id: 'pickaxe',
                name: 'Stone Pickaxe',
                type: ITEM_TYPES.TOOL,
                iconClass: 'pickaxe',
                description: 'Used for mining stone and ore blocks.'
            },
            // Consumables
            HEALTH_POTION: {
                id: 'health_potion',
                name: 'Health Potion',
                type: ITEM_TYPES.CONSUMABLE,
                iconClass: 'health_potion',
                healAmount: 50,
                description: 'Restores 50 health points when consumed.'
            }
        };
        
        // Biomes
        const BIOMES = {
            PLAINS: 'plains',
            FOREST: 'forest',
            DESERT: 'desert',
            SNOW: 'snow',
            MOUNTAINS: 'mountains'
        };
        
        function getBiome(x, z) {
            const temp = noise.octaveNoise(x * 0.01, z * 0.01, 2);
            const humidity = noise.octaveNoise(x * 0.008 + 1000, z * 0.008 + 1000, 2);
            const height = getHeightAt(x, z);
            
            if (height > 20) return BIOMES.MOUNTAINS;
            if (temp < 0.3) return BIOMES.SNOW;
            if (humidity < 0.3) return BIOMES.DESERT;
            if (humidity > 0.7) return BIOMES.FOREST;
            return BIOMES.PLAINS;
        }
        
        function getHeightAt(x, z) {
            let height = noise.octaveNoise(x * 0.005, z * 0.005, 4) * 15;
            height += noise.octaveNoise(x * 0.02, z * 0.02, 2) * 5;
            return Math.floor(height + SEA_LEVEL);
        }
        
        function getBlockType(x, y, z, biome) {
            const height = getHeightAt(x, z);
            
            // Water
            if (y <= SEA_LEVEL && y > height) {
                return BLOCKS.WATER;
            }
            
            // Air
            if (y > height) return BLOCKS.AIR;
            
            // Underground caves (simple 3D noise)
            if (y < height - 2 && y > 2) {
                const caveNoise1 = noise.octaveNoise(x * 0.03, y * 0.03, 2);
                const caveNoise2 = noise.octaveNoise(z * 0.03, y * 0.03, 2);
                const caveNoise3 = noise.octaveNoise(x * 0.02, z * 0.02, 2);
                
                // Combine noise for 3D cave system
                const caveValue = (caveNoise1 + caveNoise2 + caveNoise3) / 3;
                
                if (caveValue > 0.6) {
                    return BLOCKS.AIR; // Cave space
                }
            }
            
            // Surface
            if (y === height) {
                switch(biome) {
                    case BIOMES.DESERT: return BLOCKS.SAND;
                    case BIOMES.SNOW: return BLOCKS.SNOW;
                    default: return BLOCKS.GRASS;
                }
            }
            
            // Subsurface
            if (y > height - 3) {
                return biome === BIOMES.DESERT ? BLOCKS.SAND : BLOCKS.DIRT;
            }
            
            return BLOCKS.STONE;
        }
        
        // Chunk class - simplified for performance
        class Chunk {
            constructor(chunkX, chunkZ) {
                this.x = chunkX;
                this.z = chunkZ;
                this.blocks = new Array(CHUNK_SIZE * CHUNK_SIZE * WORLD_HEIGHT);
                this.mesh = null;
                this.biome = getBiome(chunkX * CHUNK_SIZE + 8, chunkZ * CHUNK_SIZE + 8);
                
                this.generate();
            }
            
            generate() {
                const startX = this.x * CHUNK_SIZE;
                const startZ = this.z * CHUNK_SIZE;
                
                for (let x = 0; x < CHUNK_SIZE; x++) {
                    for (let z = 0; z < CHUNK_SIZE; z++) {
                        const worldX = startX + x;
                        const worldZ = startZ + z;
                        const biome = getBiome(worldX, worldZ);
                        
                        for (let y = 0; y < WORLD_HEIGHT; y++) {
                            const blockType = getBlockType(worldX, y, worldZ, biome);
                            this.setBlock(x, y, z, blockType);
                        }
                    }
                }
                
                // Add some trees (simplified)
                if (this.biome === BIOMES.FOREST || this.biome === BIOMES.PLAINS) {
                    this.generateTrees();
                }
            }
            
            generateTrees() {
                for (let x = 2; x < CHUNK_SIZE - 2; x += 4) {
                    for (let z = 2; z < CHUNK_SIZE - 2; z += 4) {
                        const worldX = this.x * CHUNK_SIZE + x;
                        const worldZ = this.z * CHUNK_SIZE + z;
                        const treeNoise = noise.noise(worldX * 0.1, worldZ * 0.1);
                        
                        if (treeNoise > 0.6) {
                            const height = getHeightAt(worldX, worldZ);
                            if (height > SEA_LEVEL) {
                                this.generateTree(x, height + 1, z);
                            }
                        }
                    }
                }
            }
            
            generateTree(x, y, z) {
                const treeHeight = 4 + Math.floor(Math.random() * 3);
                
                // Generate trunk
                for (let i = 0; i < treeHeight; i++) {
                    if (y + i < WORLD_HEIGHT) {
                        this.setBlock(x, y + i, z, BLOCKS.WOOD);
                    }
                }
                
                // Generate beautiful leafy canopy
                const leafY = y + treeHeight;
                for (let dx = -2; dx <= 2; dx++) {
                    for (let dz = -2; dz <= 2; dz++) {
                        for (let dy = -1; dy <= 1; dy++) {
                            // Create a nice rounded tree shape
                            if (Math.abs(dx) + Math.abs(dz) + Math.abs(dy) <= 3) {
                                const leafX = x + dx;
                                const leafZ = z + dz;
                                const leafYPos = leafY + dy;
                                
                                if (leafX >= 0 && leafX < CHUNK_SIZE && 
                                    leafZ >= 0 && leafZ < CHUNK_SIZE && 
                                    leafYPos < WORLD_HEIGHT && leafYPos > 0) {
                                    
                                    if (this.getBlock(leafX, leafYPos, leafZ) === BLOCKS.AIR) {
                                        this.setBlock(leafX, leafYPos, leafZ, BLOCKS.LEAVES);
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Add extra leaves on top for better shape
                if (leafY + 2 < WORLD_HEIGHT) {
                    this.setBlock(x, leafY + 2, z, BLOCKS.LEAVES);
                }
            }
            
            setBlock(x, y, z, blockType) {
                const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
                this.blocks[index] = blockType;
            }
            
            getBlock(x, y, z) {
                if (x < 0 || x >= CHUNK_SIZE || y < 0 || y >= WORLD_HEIGHT || z < 0 || z >= CHUNK_SIZE) {
                    return BLOCKS.AIR;
                }
                const index = x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
                return this.blocks[index] || BLOCKS.AIR;
            }
            
            createMesh() {
                if (this.mesh) {
                    scene.remove(this.mesh);
                    if (this.mesh.geometry) this.mesh.geometry.dispose();
                }
                
                const geometry = new THREE.BufferGeometry();
                const vertices = [];
                const colors = [];
                const indices = [];
                let vertexIndex = 0;
                
                const blockColors = {
                    [BLOCKS.GRASS]: [0.3, 0.7, 0.3],
                    [BLOCKS.DIRT]: [0.5, 0.3, 0.1],
                    [BLOCKS.STONE]: [0.5, 0.5, 0.5],
                    [BLOCKS.SAND]: [0.9, 0.7, 0.4],
                    [BLOCKS.SNOW]: [1, 1, 1],
                    [BLOCKS.WATER]: [0, 0.5, 0.8],
                    [BLOCKS.WOOD]: [0.4, 0.2, 0.1],
                    [BLOCKS.LEAVES]: [0.1, 0.5, 0.1]
                };
                
                for (let x = 0; x < CHUNK_SIZE; x++) {
                    for (let y = 0; y < WORLD_HEIGHT; y++) {
                        for (let z = 0; z < CHUNK_SIZE; z++) {
                            const blockType = this.getBlock(x, y, z);
                            
                            if (blockType === BLOCKS.AIR) continue;
                            
                            const worldX = this.x * CHUNK_SIZE + x;
                            const worldZ = this.z * CHUNK_SIZE + z;
                            const color = blockColors[blockType] || [0.5, 0.5, 0.5];
                            
                            // Check each face
                            const faces = [
                                { dir: [0, 1, 0], verts: [[0,1,1], [1,1,1], [1,1,0], [0,1,0]] }, // top
                                { dir: [0, -1, 0], verts: [[0,0,0], [1,0,0], [1,0,1], [0,0,1]] }, // bottom
                                { dir: [1, 0, 0], verts: [[1,0,0], [1,1,0], [1,1,1], [1,0,1]] }, // right
                                { dir: [-1, 0, 0], verts: [[0,0,1], [0,1,1], [0,1,0], [0,0,0]] }, // left
                                { dir: [0, 0, 1], verts: [[1,0,1], [1,1,1], [0,1,1], [0,0,1]] }, // front
                                { dir: [0, 0, -1], verts: [[0,0,0], [0,1,0], [1,1,0], [1,0,0]] }  // back
                            ];
                            
                            faces.forEach(face => {
                                const [dx, dy, dz] = face.dir;
                                const nx = x + dx;
                                const ny = y + dy;
                                const nz = z + dz;
                                
                                let neighborType = BLOCKS.AIR;
                                if (nx >= 0 && nx < CHUNK_SIZE && ny >= 0 && ny < WORLD_HEIGHT && nz >= 0 && nz < CHUNK_SIZE) {
                                    neighborType = this.getBlock(nx, ny, nz);
                                }
                                
                                if (neighborType === BLOCKS.AIR || (blockType !== BLOCKS.WATER && neighborType === BLOCKS.WATER)) {
                                    face.verts.forEach(vert => {
                                        vertices.push(
                                            worldX + vert[0],
                                            y + vert[1],
                                            worldZ + vert[2]
                                        );
                                        colors.push(color[0], color[1], color[2]);
                                    });
                                    
                                    indices.push(
                                        vertexIndex, vertexIndex + 1, vertexIndex + 2,
                                        vertexIndex, vertexIndex + 2, vertexIndex + 3
                                    );
                                    vertexIndex += 4;
                                }
                            });
                        }
                    }
                }
                
                if (vertices.length > 0) {
                    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
                    geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
                    geometry.setIndex(indices);
                    geometry.computeVertexNormals();
                    
                    const material = new THREE.MeshLambertMaterial({ 
                        vertexColors: true,
                        side: THREE.DoubleSide
                    });
                    
                    this.mesh = new THREE.Mesh(geometry, material);
                    scene.add(this.mesh);
                }
            }
        }
        
        // Simple Orc enemy class
        class Orc {
            constructor(x, z) {
                this.position = new THREE.Vector3(x, getHeightAt(x, z) + 2, z);
                this.velocity = new THREE.Vector3(0, 0, 0);
                this.health = 100;
                this.maxHealth = 100;
                this.speed = 2.5;
                this.attackRange = 3;
                this.detectionRange = 15;
                this.attackCooldown = 0;
                this.attackDamage = 15;
                this.onGround = false;
                this.state = 'idle';
                this.mesh = null;
                this.healthBar = null;
                
                this.createMesh();
            }
            
            createMesh() {
                // Create orc body (green)
                const bodyGeometry = new THREE.BoxGeometry(0.8, 1.6, 0.4);
                const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5d23 });
                const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
                
                // Create orc head
                const headGeometry = new THREE.BoxGeometry(0.6, 0.6, 0.6);
                const headMaterial = new THREE.MeshLambertMaterial({ color: 0x5d6b2a });
                const head = new THREE.Mesh(headGeometry, headMaterial);
                head.position.y = 1.1;
                
                // Create red eyes
                const eyeGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
                const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
                const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
                const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
                leftEye.position.set(-0.15, 1.15, 0.25);
                rightEye.position.set(0.15, 1.15, 0.25);
                
                // Create arms
                const armGeometry = new THREE.BoxGeometry(0.3, 1.2, 0.3);
                const armMaterial = new THREE.MeshLambertMaterial({ color: 0x4a5d23 });
                const leftArm = new THREE.Mesh(armGeometry, armMaterial);
                const rightArm = new THREE.Mesh(armGeometry, armMaterial);
                leftArm.position.set(-0.6, 0.2, 0);
                rightArm.position.set(0.6, 0.2, 0);
                
                // Create legs
                const legGeometry = new THREE.BoxGeometry(0.3, 1.0, 0.3);
                const legMaterial = new THREE.MeshLambertMaterial({ color: 0x3a4d1a });
                const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
                const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
                leftLeg.position.set(-0.2, -1.3, 0);
                rightLeg.position.set(0.2, -1.3, 0);
                
                // Group all parts
                this.mesh = new THREE.Group();
                this.mesh.add(body);
                this.mesh.add(head);
                this.mesh.add(leftEye);
                this.mesh.add(rightEye);
                this.mesh.add(leftArm);
                this.mesh.add(rightArm);
                this.mesh.add(leftLeg);
                this.mesh.add(rightLeg);
                
                this.mesh.position.copy(this.position);
                scene.add(this.mesh);
                
                // Create simple health bar
                this.createHealthBar();
            }
            
            createHealthBar() {
                const barWidth = 1.5;
                const barHeight = 0.1;
                
                // Background bar (red)
                const bgGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
                const bgMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
                const bgBar = new THREE.Mesh(bgGeometry, bgMaterial);
                
                // Health bar (green)
                const healthGeometry = new THREE.PlaneGeometry(barWidth, barHeight);
                const healthMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const healthBar = new THREE.Mesh(healthGeometry, healthMaterial);
                
                this.healthBar = new THREE.Group();
                this.healthBar.add(bgBar);
                this.healthBar.add(healthBar);
                this.healthBar.position.set(0, 2.5, 0);
                
                this.mesh.add(this.healthBar);
                this.healthBarMesh = healthBar;
            }
            
            update(deltaTime, playerPosition) {
                if (this.health <= 0) return;
                
                // Apply gravity
                this.velocity.y -= 25 * deltaTime;
                
                // Calculate distance to player
                const distanceToPlayer = this.position.distanceTo(playerPosition);
                
                // Update state based on distance
                if (distanceToPlayer <= this.attackRange) {
                    this.state = 'attacking';
                } else if (distanceToPlayer <= this.detectionRange) {
                    this.state = 'chasing';
                } else {
                    this.state = 'idle';
                }
                
                // Behavior based on state
                switch (this.state) {
                    case 'chasing':
                        this.chasePlayer(playerPosition, deltaTime);
                        break;
                    case 'attacking':
                        this.attackPlayer(playerPosition, deltaTime);
                        break;
                }
                
                // Update position
                this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
                
                // Ground collision
                const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z)) + 1;
                if (this.position.y <= groundHeight) {
                    this.position.y = groundHeight;
                    this.velocity.y = 0;
                    this.onGround = true;
                } else {
                    this.onGround = false;
                }
                
                // Update mesh position
                if (this.mesh) {
                    this.mesh.position.copy(this.position);
                    
                    // Make orc face the player
                    if (this.state !== 'idle') {
                        this.mesh.lookAt(playerPosition.x, this.position.y, playerPosition.z);
                    }
                }
                
                // Update health bar
                this.updateHealthBar();
                
                // Update attack cooldown
                if (this.attackCooldown > 0) {
                    this.attackCooldown -= deltaTime;
                }
            }
            
            chasePlayer(playerPosition, deltaTime) {
                const direction = new THREE.Vector3()
                    .subVectors(playerPosition, this.position)
                    .normalize();
                
                this.velocity.x = direction.x * this.speed;
                this.velocity.z = direction.z * this.speed;
            }
            
            attackPlayer(playerPosition, deltaTime) {
                if (this.attackCooldown <= 0) {
                    // Deal damage to player
                    player.takeDamage(this.attackDamage);
                    this.attackCooldown = 2.0;
                    
                    // Visual attack effect
                    if (this.mesh) {
                        this.mesh.scale.set(1.2, 1.2, 1.2);
                        setTimeout(() => {
                            if (this.mesh) this.mesh.scale.set(1, 1, 1);
                        }, 200);
                    }
                }
                
                // Stop moving when attacking
                this.velocity.x = 0;
                this.velocity.z = 0;
            }
            
            takeDamage(damage) {
                this.health -= damage;
                if (this.health <= 0) {
                    this.die();
                }
            }
            
            updateHealthBar() {
                if (this.healthBar && this.healthBarMesh) {
                    const healthPercent = this.health / this.maxHealth;
                    this.healthBarMesh.scale.x = healthPercent;
                    this.healthBarMesh.position.x = -(1.5 * (1 - healthPercent)) / 2;
                    
                    // Make health bar face camera
                    this.healthBar.lookAt(camera.position);
                }
            }
            
            die() {
                if (this.mesh) {
                    scene.remove(this.mesh);
                    this.mesh = null;
                }
                this.health = 0;
            }
        }
        
        // Enhanced Inventory system
        class Inventory {
            constructor() {
                this.slots = new Array(20).fill(null); // 20 inventory slots
                this.selectedSlot = -1;
                this.currentWeapon = ITEMS.FIST;
                this.kills = 0;
                this.score = 0;
                this.isOpen = false;

                this.initializeInventoryUI();
                this.setupInventoryControls();
            }

            initializeInventoryUI() {
                const grid = document.getElementById('inventoryGrid');
                grid.innerHTML = '';

                for (let i = 0; i < 20; i++) {
                    const slot = document.createElement('div');
                    slot.className = 'inventory-slot';
                    slot.dataset.slotIndex = i;

                    slot.addEventListener('click', (e) => this.selectSlot(i));
                    slot.addEventListener('contextmenu', (e) => {
                        e.preventDefault();
                        this.dropItem(i);
                    });

                    grid.appendChild(slot);
                }

                this.updateInventoryDisplay();
            }

            setupInventoryControls() {
                document.addEventListener('keydown', (e) => {
                    if (e.code === 'KeyI' || (e.code === 'Escape' && this.isOpen)) {
                        e.preventDefault();
                        this.toggleInventory();
                    }
                });
            }

            toggleInventory() {
                this.isOpen = !this.isOpen;
                const panel = document.getElementById('inventoryPanel');
                panel.style.display = this.isOpen ? 'block' : 'none';

                if (this.isOpen) {
                    this.updateInventoryDisplay();
                    // Release pointer lock when inventory is open
                    if (document.pointerLockElement) {
                        document.exitPointerLock();
                    }
                }
            }

            addItem(itemId, count = 1) {
                const item = ITEMS[itemId];
                if (!item) return false;

                // Try to stack with existing items first
                for (let i = 0; i < this.slots.length; i++) {
                    if (this.slots[i] && this.slots[i].id === itemId) {
                        this.slots[i].count += count;
                        this.updateInventoryDisplay();
                        return true;
                    }
                }

                // Find empty slot
                for (let i = 0; i < this.slots.length; i++) {
                    if (!this.slots[i]) {
                        this.slots[i] = {
                            id: itemId,
                            item: item,
                            count: count
                        };
                        this.updateInventoryDisplay();
                        return true;
                    }
                }

                return false; // Inventory full
            }

            removeItem(slotIndex, count = 1) {
                if (!this.slots[slotIndex]) return false;

                this.slots[slotIndex].count -= count;
                if (this.slots[slotIndex].count <= 0) {
                    this.slots[slotIndex] = null;
                    if (this.selectedSlot === slotIndex) {
                        this.selectedSlot = -1;
                        this.updateSelectedItemInfo();
                    }
                }

                this.updateInventoryDisplay();
                return true;
            }

            selectSlot(slotIndex) {
                this.selectedSlot = slotIndex;
                this.updateInventoryDisplay();
                this.updateSelectedItemInfo();

                // If it's a weapon, equip it
                const slotItem = this.slots[slotIndex];
                if (slotItem && slotItem.item.type === ITEM_TYPES.WEAPON) {
                    this.currentWeapon = slotItem.item;
                    this.updateUI();
                    // Update character UI to show new equipment bonuses
                    gameState.updateCharacterUI();
                    player.showMessage(`Equipped ${slotItem.item.name}!`, 'blue');
                }

                // If it's a consumable, use it
                if (slotItem && slotItem.item.type === ITEM_TYPES.CONSUMABLE) {
                    this.useConsumable(slotIndex);
                }
            }

            useConsumable(slotIndex) {
                const slotItem = this.slots[slotIndex];
                if (!slotItem || slotItem.item.type !== ITEM_TYPES.CONSUMABLE) return;

                if (slotItem.item.id === 'health_potion') {
                    player.heal(slotItem.item.healAmount);
                    this.removeItem(slotIndex, 1);
                    player.showMessage(`+${slotItem.item.healAmount} Health!`, 'green');
                }
            }

            dropItem(slotIndex) {
                if (!this.slots[slotIndex]) return;

                const item = this.slots[slotIndex];
                console.log(`Dropped ${item.item.name} x${item.count}`);
                player.showMessage(`Dropped ${item.item.name}`, 'orange');

                this.slots[slotIndex] = null;
                if (this.selectedSlot === slotIndex) {
                    this.selectedSlot = -1;
                    this.updateSelectedItemInfo();
                }

                this.updateInventoryDisplay();
            }

            updateInventoryDisplay() {
                const slots = document.querySelectorAll('.inventory-slot');

                slots.forEach((slot, index) => {
                    const slotData = this.slots[index];

                    // Clear slot
                    slot.innerHTML = '';
                    slot.className = 'inventory-slot';

                    if (slotData) {
                        slot.classList.add('has-item');

                        // Use 3D rendered icons for all items now
                        if (itemIconRenderer) {
                            const iconImg = document.createElement('img');
                            iconImg.className = 'item-icon rendered-icon';
                            iconImg.src = itemIconRenderer.renderIcon(slotData.item.id);
                            iconImg.style.width = '56px';
                            iconImg.style.height = '56px';
                            iconImg.style.imageRendering = 'pixelated';
                            slot.appendChild(iconImg);
                        } else {
                            // Fallback to CSS icons if 3D renderer not available
                            const icon = document.createElement('div');
                            icon.className = `item-icon ${slotData.item.iconClass}`;
                            slot.appendChild(icon);
                        }

                        if (slotData.count > 1) {
                            const count = document.createElement('div');
                            count.className = 'item-count';
                            count.textContent = slotData.count;
                            slot.appendChild(count);
                        }
                    }

                    if (index === this.selectedSlot) {
                        slot.classList.add('selected');
                    }
                });
            }

            updateSelectedItemInfo() {
                const nameElement = document.getElementById('selectedItemName');
                const descElement = document.getElementById('selectedItemDesc');

                if (this.selectedSlot >= 0 && this.slots[this.selectedSlot]) {
                    const slotData = this.slots[this.selectedSlot];
                    nameElement.textContent = `${slotData.item.name} x${slotData.count}`;
                    descElement.textContent = slotData.item.description;
                } else {
                    nameElement.textContent = 'Select an item to see details';
                    descElement.textContent = '';
                }
            }

            addKill() {
                this.kills++;
                this.score += 100;
                this.updateUI();
            }

            getWeaponDamage() {
                return this.currentWeapon.damage || 25;
            }

            getWeaponName() {
                return this.currentWeapon.name || 'Fist';
            }

            // Get equipment bonuses for character stats
            getEquipmentBonuses() {
                let bonuses = {
                    strength: 0,
                    defense: 0,
                    magic: 0,
                    health: 0
                };

                // Check all inventory slots for equipment
                this.slots.forEach(slot => {
                    if (slot && slot.item) {
                        const item = slot.item;

                        // Add bonuses based on item type
                        if (item.type === 'WEAPON') {
                            bonuses.strength += item.damage || 0;
                        } else if (item.id === 'SHIELD' || item.name.includes('Shield')) {
                            bonuses.defense += 5; // Shield bonus
                        } else if (item.id === 'MAGIC_STAFF' || item.name.includes('Staff')) {
                            bonuses.magic += 8; // Staff bonus
                        }
                    }
                });

                return bonuses;
            }

            updateUI() {
                document.getElementById('currentWeapon').textContent = this.getWeaponName();
                document.getElementById('swordCount').textContent = this.countItem('sword');
                document.getElementById('killCount').textContent = this.kills;
                document.getElementById('score').textContent = this.score;
            }

            countItem(itemId) {
                let total = 0;
                for (const slot of this.slots) {
                    if (slot && slot.id === itemId) {
                        total += slot.count;
                    }
                }
                return total;
            }
        }

        // Story Locations (from game.py)
        const STORY_LOCATIONS = {
            SPAWN: { x: 0, z: 0, name: "Starting Area", description: "Where your adventure begins..." },
            TOWN: { x: 100, z: 50, name: "Town", description: "A peaceful town with cobblestone streets and shops." },
            FOREST: { x: -80, z: 100, name: "Forest", description: "A dense forest with tall trees. You hear rustling in the bushes." },
            CAVE: { x: 150, z: -50, name: "Cave", description: "A dark cave with mysterious sounds echoing from within." },
            DUNGEON: { x: 200, z: -100, name: "Dungeon", description: "A deep dungeon with ancient stone walls." },
            VILLAGE: { x: -150, z: 200, name: "Village", description: "A peaceful village with friendly blacksmiths." },
            ROAD: { x: 0, z: 150, name: "Road", description: "A long, winding road stretches into the distance." },
            FIELD: { x: -100, z: 250, name: "Field", description: "A wide open field with tall grass and wildflowers." }
        };

        // World class - enhanced with boundaries and story locations
        class World {
            constructor() {
                this.chunks = new Map();
                this.orcs = [];
                this.maxOrcs = 5;
                this.orcSpawnTimer = 0;

                // World boundaries (limit the world size)
                this.worldBounds = {
                    minX: -300,
                    maxX: 300,
                    minZ: -300,
                    maxZ: 300
                };

                this.currentLocation = "Starting Area";
                this.locationCheckTimer = 0;

                // Building collision boxes
                this.buildingCollisions = [];

                // Create location markers in the world
                this.createLocationMarkers();
            }
            
            createLocationMarkers() {
                // Create structures for each story location
                Object.entries(STORY_LOCATIONS).forEach(([key, location]) => {
                    if (key === 'SPAWN') return; // Skip spawn point

                    const groundHeight = getHeightAt(location.x, location.z);

                    if (key === 'TOWN') {
                        this.createTown(location.x, location.z, groundHeight);
                    } else if (key === 'VILLAGE') {
                        this.createVillage(location.x, location.z, groundHeight);
                    } else if (key === 'CAVE') {
                        this.createCaveEntrance(location.x, location.z, groundHeight);
                    } else {
                        // Create simple marker for other locations
                        this.createSimpleMarker(location.x, location.z, groundHeight, location.name, key);
                    }

                    console.log(`Created ${location.name} at (${location.x}, ${location.z})`);
                });
            }

            createTown(x, z, groundHeight) {
                // Create town center with multiple buildings
                const buildings = [
                    { x: 0, z: 0, width: 12, height: 8, depth: 12, color: 0x8B4513, name: "Town Hall" },
                    { x: -20, z: -15, width: 8, height: 6, depth: 10, color: 0xA0522D, name: "Shop" },
                    { x: 20, z: -15, width: 8, height: 6, depth: 8, color: 0xD2B48C, name: "House" },
                    { x: -15, z: 20, width: 6, height: 5, depth: 8, color: 0xDEB887, name: "House" },
                    { x: 15, z: 20, width: 7, height: 5, depth: 9, color: 0xF5DEB3, name: "House" },
                    { x: 0, z: -30, width: 10, height: 7, depth: 8, color: 0x654321, name: "Inn" }
                ];

                buildings.forEach(building => {
                    const buildingX = x + building.x;
                    const buildingZ = z + building.z;
                    const buildingGroundHeight = getHeightAt(buildingX, buildingZ);

                    // Main building structure
                    const buildingGeometry = new THREE.BoxGeometry(building.width, building.height, building.depth);
                    const buildingMaterial = new THREE.MeshLambertMaterial({ color: building.color });
                    const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
                    buildingMesh.position.set(buildingX, buildingGroundHeight + building.height/2, buildingZ);
                    scene.add(buildingMesh);

                    // Add collision box
                    this.addBuildingCollision(buildingX, buildingZ, buildingGroundHeight, building.width, building.height, building.depth);

                    // Roof
                    const roofGeometry = new THREE.ConeGeometry(Math.max(building.width, building.depth) * 0.7, 3, 4);
                    const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
                    const roof = new THREE.Mesh(roofGeometry, roofMaterial);
                    roof.position.set(x + building.x, groundHeight + building.height + 1.5, z + building.z);
                    roof.rotation.y = Math.PI / 4;
                    scene.add(roof);

                    // Door
                    const doorGeometry = new THREE.BoxGeometry(1.5, 3, 0.2);
                    const doorMaterial = new THREE.MeshLambertMaterial({ color: 0x4A4A4A });
                    const door = new THREE.Mesh(doorGeometry, doorMaterial);
                    door.position.set(x + building.x, groundHeight + 1.5, z + building.z + building.depth/2 + 0.1);
                    scene.add(door);

                    // Windows
                    for (let i = 0; i < 2; i++) {
                        const windowGeometry = new THREE.BoxGeometry(1, 1, 0.1);
                        const windowMaterial = new THREE.MeshLambertMaterial({ color: 0x87CEEB });
                        const window = new THREE.Mesh(windowGeometry, windowMaterial);
                        window.position.set(
                            x + building.x + (i === 0 ? -2 : 2),
                            groundHeight + 3,
                            z + building.z + building.depth/2 + 0.1
                        );
                        scene.add(window);
                    }
                });

                // Create cobblestone paths
                this.createCobblestoneStreets(x, z, groundHeight);

                // Add decorative elements
                this.addTownDecorations(x, z, groundHeight);

                // Add town sign
                this.createTownSign(x, z, groundHeight, "TOWN");
            }

            createCobblestoneStreets(centerX, centerZ, groundHeight) {
                // Main street (north-south)
                for (let i = -40; i <= 40; i += 2) {
                    const stoneGeometry = new THREE.BoxGeometry(2, 0.2, 2);
                    const stoneMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
                    const stone = new THREE.Mesh(stoneGeometry, stoneMaterial);
                    stone.position.set(centerX, groundHeight + 0.1, centerZ + i);
                    scene.add(stone);
                }

                // Cross street (east-west)
                for (let i = -30; i <= 30; i += 2) {
                    const stoneGeometry = new THREE.BoxGeometry(2, 0.2, 2);
                    const stoneMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
                    const stone = new THREE.Mesh(stoneGeometry, stoneMaterial);
                    stone.position.set(centerX + i, groundHeight + 0.1, centerZ);
                    scene.add(stone);
                }
            }

            createVillage(x, z, groundHeight) {
                // Smaller village with blacksmith
                const buildings = [
                    { x: 0, z: 0, width: 10, height: 6, depth: 8, color: 0x654321, name: "Blacksmith" },
                    { x: -15, z: 10, width: 6, height: 5, depth: 6, color: 0x8B4513, name: "House" },
                    { x: 15, z: -10, width: 6, height: 5, depth: 6, color: 0xA0522D, name: "House" }
                ];

                buildings.forEach(building => {
                    const buildingGeometry = new THREE.BoxGeometry(building.width, building.height, building.depth);
                    const buildingMaterial = new THREE.MeshLambertMaterial({ color: building.color });
                    const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
                    buildingMesh.position.set(x + building.x, groundHeight + building.height/2, z + building.z);
                    scene.add(buildingMesh);

                    // Simple roof
                    const roofGeometry = new THREE.BoxGeometry(building.width + 1, 1, building.depth + 1);
                    const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
                    const roof = new THREE.Mesh(roofGeometry, roofMaterial);
                    roof.position.set(x + building.x, groundHeight + building.height + 0.5, z + building.z);
                    scene.add(roof);
                });

                // Add anvil for blacksmith
                const anvilGeometry = new THREE.BoxGeometry(2, 1, 1);
                const anvilMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F4F });
                const anvil = new THREE.Mesh(anvilGeometry, anvilMaterial);
                anvil.position.set(x + 3, groundHeight + 0.5, z);
                scene.add(anvil);

                this.createTownSign(x, z, groundHeight, "VILLAGE");
            }

            createCaveEntrance(x, z, groundHeight) {
                // Create cave entrance
                const caveGeometry = new THREE.SphereGeometry(8, 8, 8, 0, Math.PI);
                const caveMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
                const cave = new THREE.Mesh(caveGeometry, caveMaterial);
                cave.position.set(x, groundHeight + 4, z);
                cave.rotation.x = Math.PI / 2;
                scene.add(cave);

                this.createTownSign(x, z + 15, groundHeight, "CAVE");
            }

            createSimpleMarker(x, z, groundHeight, name, key) {
                // Create a simple marker for other locations
                const markerGeometry = new THREE.BoxGeometry(2, 8, 2);
                const markerMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
                const marker = new THREE.Mesh(markerGeometry, markerMaterial);
                marker.position.set(x, groundHeight + 4, z);
                scene.add(marker);

                this.createTownSign(x, z, groundHeight, name.toUpperCase());
            }

            addTownDecorations(x, z, groundHeight) {
                // Add Minecraft-style block trees around the town
                const treePositions = [
                    { x: -35, z: -35 }, { x: 35, z: -35 }, { x: -35, z: 35 }, { x: 35, z: 35 },
                    { x: -25, z: 0 }, { x: 25, z: 0 }, { x: 0, z: -25 }, { x: 0, z: 35 }
                ];

                treePositions.forEach(pos => {
                    this.createMinecraftTree(x + pos.x, z + pos.z, groundHeight);
                });

                // Add a simple fountain in the center
                const fountainGeometry = new THREE.BoxGeometry(6, 2, 6);
                const fountainMaterial = new THREE.MeshLambertMaterial({ color: 0x708090 });
                const fountain = new THREE.Mesh(fountainGeometry, fountainMaterial);
                fountain.position.set(x, groundHeight + 1, z);
                scene.add(fountain);

                // Water in fountain (blue block)
                const waterGeometry = new THREE.BoxGeometry(4, 0.5, 4);
                const waterMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
                const water = new THREE.Mesh(waterGeometry, waterMaterial);
                water.position.set(x, groundHeight + 2.2, z);
                scene.add(water);
            }

            createMinecraftTree(x, z, groundHeight) {
                const treeHeight = 4 + Math.floor(Math.random() * 3);

                // Generate trunk (wood blocks)
                for (let i = 0; i < treeHeight; i++) {
                    const trunkGeometry = new THREE.BoxGeometry(1, 1, 1);
                    const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                    const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
                    trunk.position.set(x, groundHeight + i + 0.5, z);
                    scene.add(trunk);
                }

                // Generate leafy canopy (leaf blocks)
                const leafY = groundHeight + treeHeight;
                for (let dx = -2; dx <= 2; dx++) {
                    for (let dz = -2; dz <= 2; dz++) {
                        for (let dy = -1; dy <= 1; dy++) {
                            // Create a nice rounded tree shape
                            if (Math.abs(dx) + Math.abs(dz) + Math.abs(dy) <= 3) {
                                const leafGeometry = new THREE.BoxGeometry(1, 1, 1);
                                const leafMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
                                const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
                                leaf.position.set(x + dx, leafY + dy + 0.5, z + dz);
                                scene.add(leaf);
                            }
                        }
                    }
                }

                // Add extra leaves on top
                const topLeafGeometry = new THREE.BoxGeometry(1, 1, 1);
                const topLeafMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
                const topLeaf = new THREE.Mesh(topLeafGeometry, topLeafMaterial);
                topLeaf.position.set(x, leafY + 2.5, z);
                scene.add(topLeaf);
            }

            createTownSign(x, z, groundHeight, text) {
                // Create a simple sign post
                const postGeometry = new THREE.BoxGeometry(0.3, 4, 0.3);
                const postMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                const post = new THREE.Mesh(postGeometry, postMaterial);
                post.position.set(x - 8, groundHeight + 2, z - 8);
                scene.add(post);

                // Sign board
                const signGeometry = new THREE.BoxGeometry(4, 1, 0.2);
                const signMaterial = new THREE.MeshLambertMaterial({ color: 0xF5DEB3 });
                const sign = new THREE.Mesh(signGeometry, signMaterial);
                sign.position.set(x - 8, groundHeight + 3, z - 8);
                scene.add(sign);
            }

            checkWorldBounds(position) {
                // Enforce world boundaries
                let bounded = false;

                if (position.x < this.worldBounds.minX) {
                    position.x = this.worldBounds.minX;
                    bounded = true;
                }
                if (position.x > this.worldBounds.maxX) {
                    position.x = this.worldBounds.maxX;
                    bounded = true;
                }
                if (position.z < this.worldBounds.minZ) {
                    position.z = this.worldBounds.minZ;
                    bounded = true;
                }
                if (position.z > this.worldBounds.maxZ) {
                    position.z = this.worldBounds.maxZ;
                    bounded = true;
                }

                if (bounded) {
                    player.showMessage("You've reached the edge of the world!", 'orange');
                }

                return bounded;
            }

            checkBuildingCollision(position) {
                // Check collision with buildings
                for (const building of this.buildingCollisions) {
                    if (position.x >= building.minX && position.x <= building.maxX &&
                        position.z >= building.minZ && position.z <= building.maxZ &&
                        position.y >= building.minY && position.y <= building.maxY) {
                        return true; // Collision detected
                    }
                }
                return false;
            }

            addBuildingCollision(x, z, groundHeight, width, height, depth) {
                this.buildingCollisions.push({
                    minX: x - width/2,
                    maxX: x + width/2,
                    minY: groundHeight,
                    maxY: groundHeight + height,
                    minZ: z - depth/2,
                    maxZ: z + depth/2
                });
            }

            checkPlayerLocation(playerPosition) {
                // Check if player is near any story location
                let nearestLocation = "Wilderness";
                let nearestDistance = Infinity;

                Object.entries(STORY_LOCATIONS).forEach(([key, location]) => {
                    const distance = Math.sqrt(
                        Math.pow(playerPosition.x - location.x, 2) +
                        Math.pow(playerPosition.z - location.z, 2)
                    );

                    // If within 30 units of a location, consider player there
                    if (distance < 30 && distance < nearestDistance) {
                        nearestDistance = distance;
                        nearestLocation = location.name;
                    }
                });

                // Update location if changed
                if (nearestLocation !== this.currentLocation) {
                    this.currentLocation = nearestLocation;
                    const locationData = Object.values(STORY_LOCATIONS).find(loc => loc.name === nearestLocation);
                    if (locationData) {
                        player.showMessage(`Entered: ${nearestLocation}`, 'cyan');
                        console.log(`Player entered: ${nearestLocation} - ${locationData.description}`);
                    }

                    // Update biome display to show current location
                    document.getElementById('biome').textContent = nearestLocation;
                }
            }

            updateCompass(playerPosition) {
                // Calculate direction and distance to town
                const townLocation = STORY_LOCATIONS.TOWN;
                const dx = townLocation.x - playerPosition.x;
                const dz = townLocation.z - playerPosition.z;
                const distance = Math.sqrt(dx * dx + dz * dz);

                // Calculate direction
                let direction = "";
                if (Math.abs(dx) > Math.abs(dz)) {
                    direction = dx > 0 ? "East" : "West";
                } else {
                    direction = dz > 0 ? "South" : "North";
                }

                // Add secondary direction for more precision
                if (Math.abs(dx) > 10 && Math.abs(dz) > 10) {
                    const secondary = dz > 0 ? "South" : "North";
                    if (direction !== secondary) {
                        direction = direction + "-" + secondary;
                    }
                }

                // Direction arrow
                let arrow = "→";
                if (direction.includes("North")) arrow = "↑";
                else if (direction.includes("South")) arrow = "↓";
                else if (direction.includes("West")) arrow = "←";

                // Update compass display
                const compassText = distance < 20 ? "Nearby!" : `${arrow} ${Math.floor(distance)}m ${direction}`;
                document.getElementById('townDirection').textContent = compassText;
            }

            getChunkKey(chunkX, chunkZ) {
                return `${chunkX},${chunkZ}`;
            }
            
            getChunk(chunkX, chunkZ) {
                // Check if chunk is within world bounds
                const chunkWorldX = chunkX * CHUNK_SIZE;
                const chunkWorldZ = chunkZ * CHUNK_SIZE;

                if (chunkWorldX < this.worldBounds.minX || chunkWorldX > this.worldBounds.maxX ||
                    chunkWorldZ < this.worldBounds.minZ || chunkWorldZ > this.worldBounds.maxZ) {
                    return null; // Don't generate chunks outside bounds
                }

                const key = this.getChunkKey(chunkX, chunkZ);
                if (!this.chunks.has(key)) {
                    this.chunks.set(key, new Chunk(chunkX, chunkZ));
                }
                return this.chunks.get(key);
            }

            updateChunks(playerX, playerZ) {
                const playerChunkX = Math.floor(playerX / CHUNK_SIZE);
                const playerChunkZ = Math.floor(playerZ / CHUNK_SIZE);

                const neededChunks = new Set();

                for (let x = playerChunkX - RENDER_DISTANCE; x <= playerChunkX + RENDER_DISTANCE; x++) {
                    for (let z = playerChunkZ - RENDER_DISTANCE; z <= playerChunkZ + RENDER_DISTANCE; z++) {
                        const chunk = this.getChunk(x, z);
                        if (chunk) { // Only process valid chunks within bounds
                            const key = this.getChunkKey(x, z);
                            neededChunks.add(key);

                            if (!chunk.mesh) {
                                chunk.createMesh();
                            }
                        }
                    }
                }

                // Remove distant chunks
                for (const [key, chunk] of this.chunks) {
                    if (!neededChunks.has(key)) {
                        if (chunk.mesh) {
                            scene.remove(chunk.mesh);
                            if (chunk.mesh.geometry) chunk.mesh.geometry.dispose();
                        }
                        this.chunks.delete(key);
                    }
                }

                // Check player location periodically
                this.locationCheckTimer += 0.1;
                if (this.locationCheckTimer > 2) { // Check every 2 seconds
                    this.checkPlayerLocation({ x: playerX, z: playerZ });
                    this.updateCompass({ x: playerX, z: playerZ });
                    this.locationCheckTimer = 0;
                }
            }
            
            updateEnemies(deltaTime, playerPosition) {
                // Update existing orcs
                this.orcs = this.orcs.filter(orc => {
                    if (orc.health <= 0) {
                        return false; // Remove dead orcs
                    }
                    orc.update(deltaTime, playerPosition);
                    return true;
                });
                
                // Spawn new orcs occasionally
                this.orcSpawnTimer += deltaTime;
                if (this.orcSpawnTimer > 10 && this.orcs.length < this.maxOrcs) {
                    this.spawnOrc(playerPosition);
                    this.orcSpawnTimer = 0;
                }
            }
            
            spawnOrc(playerPosition) {
                // Don't spawn orcs in peaceful areas like town
                if (this.currentLocation === "Town" || this.currentLocation === "Village") {
                    return;
                }

                // Spawn orc at a random location around the player
                const angle = Math.random() * Math.PI * 2;
                const distance = 20 + Math.random() * 20;

                const spawnX = playerPosition.x + Math.cos(angle) * distance;
                const spawnZ = playerPosition.z + Math.sin(angle) * distance;

                // Check if spawn location is within world bounds
                if (spawnX < this.worldBounds.minX || spawnX > this.worldBounds.maxX ||
                    spawnZ < this.worldBounds.minZ || spawnZ > this.worldBounds.maxZ) {
                    return;
                }

                // Make sure spawn location is above ground
                const groundHeight = getHeightAt(spawnX, spawnZ);
                if (groundHeight > SEA_LEVEL) {
                    const orc = new Orc(spawnX, spawnZ);
                    this.orcs.push(orc);
                    console.log(`Spawned orc at (${Math.floor(spawnX)}, ${Math.floor(spawnZ)}) in ${this.currentLocation}`);
                }
            }
        }
        
        // Player class - simplified but with stamina and combat
        class Player {
            constructor() {
                this.position = new THREE.Vector3(0, 20, 0);
                this.velocity = new THREE.Vector3(0, 0, 0);
                this.onGround = false;
                this.height = 1.8;
                this.stamina = 100;
                this.maxStamina = 100;
                this.isRunning = false;
                this.inventory = new Inventory();
                this.isDead = false;

                this.keys = {};
                this.rotation = { x: 0, y: 0 };

                this.setupControls();
            }

            // Get health from character state
            get health() {
                return gameState.character ? gameState.character.health : 100;
            }

            get maxHealth() {
                return gameState.character ? gameState.character.maxHealth : 100;
            }

            // Get combat stats from character including equipment bonuses
            getAttackDamage() {
                const baseStrength = gameState.character ? gameState.character.strength : 10;
                const weaponDamage = this.inventory.getWeaponDamage();
                const equipmentBonuses = this.inventory.getEquipmentBonuses();
                return baseStrength + weaponDamage + equipmentBonuses.strength;
            }

            getDefense() {
                const baseDefense = gameState.character ? gameState.character.defense : 8;
                const equipmentBonuses = this.inventory.getEquipmentBonuses();
                return baseDefense + equipmentBonuses.defense;
            }

            getMagicPower() {
                const baseMagic = gameState.character ? gameState.character.magic : 8;
                const equipmentBonuses = this.inventory.getEquipmentBonuses();
                return baseMagic + equipmentBonuses.magic;
            }

            takeDamage(amount) {
                if (!gameState.character) return;

                const actualDamage = Math.max(1, amount - this.getDefense());
                gameState.character.health = Math.max(0, gameState.character.health - actualDamage);
                gameState.updateCharacterUI();

                if (gameState.character.health <= 0) {
                    this.isDead = true;
                    this.showMessage('You have died! Game Over!', 'red');
                }

                return actualDamage;
            }

            heal(amount) {
                if (!gameState.character) return;

                gameState.character.health = Math.min(gameState.character.maxHealth, gameState.character.health + amount);
                gameState.updateCharacterUI();
                this.showMessage(`Healed for ${amount} HP!`, 'green');
            }
            
            setupControls() {
                document.addEventListener('keydown', (e) => {
                    // Don't prevent default for inventory key when inventory is closed
                    if (e.code !== 'KeyI' || this.inventory.isOpen) {
                        this.keys[e.code] = true;
                        if (!this.inventory.isOpen) {
                            e.preventDefault();
                        }
                    }
                });

                document.addEventListener('keyup', (e) => {
                    this.keys[e.code] = false;
                    if (!this.inventory.isOpen) {
                        e.preventDefault();
                    }
                });

                document.addEventListener('click', () => {
                    if (!document.pointerLockElement && !this.inventory.isOpen) {
                        document.body.requestPointerLock();
                    }
                });

                document.addEventListener('mousemove', (e) => {
                    if (document.pointerLockElement && !this.inventory.isOpen) {
                        this.rotation.y -= e.movementX * 0.002;
                        this.rotation.x -= e.movementY * 0.002;
                        this.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.rotation.x));
                    }
                });

                // Mouse click for attacking
                document.addEventListener('mousedown', (e) => {
                    if (document.pointerLockElement && e.button === 0 && !this.inventory.isOpen) {
                        this.attack();
                    }
                });
            }
            
            attack() {
                const attackDamage = this.getAttackDamage();
                const attackRange = 6;
                let hitSomething = false;

                // Find nearby orcs and attack them
                world.orcs.forEach(orc => {
                    if (orc.health > 0) {
                        const distance = this.position.distanceTo(orc.position);
                        if (distance <= attackRange) {
                            const wasAlive = orc.health > 0;
                            orc.takeDamage(attackDamage);
                            hitSomething = true;

                            // If orc died, give rewards
                            if (wasAlive && orc.health <= 0) {
                                this.inventory.addKill();

                                // Experience gain (from game.py - orcs give 40 exp)
                                const expGain = 40;
                                gameState.gainExperience(expGain);

                                // Gold reward (from game.py - orcs give 25 gold)
                                const goldGain = 25;
                                if (gameState.character) {
                                    gameState.character.gold += goldGain;
                                    gameState.updateCharacterUI();
                                }

                                // Random item rewards
                                const rewards = ['sword', 'health_potion', 'stone_block', 'wood_block'];
                                const randomReward = rewards[Math.floor(Math.random() * rewards.length)];
                                this.inventory.addItem(randomReward.toUpperCase());

                                // Show reward message
                                const rewardName = ITEMS[randomReward.toUpperCase()].name;
                                this.showMessage(`Orc defeated! +${expGain} EXP, +${goldGain} Gold, +1 ${rewardName}!`, 'gold');
                            }
                        }
                    }
                });
                
                // Visual feedback
                if (hitSomething) {
                    const flash = document.createElement('div');
                    flash.style.position = 'fixed';
                    flash.style.top = '0';
                    flash.style.left = '0';
                    flash.style.width = '100%';
                    flash.style.height = '100%';
                    flash.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
                    flash.style.pointerEvents = 'none';
                    flash.style.zIndex = '1000';
                    document.body.appendChild(flash);
                    
                    setTimeout(() => {
                        document.body.removeChild(flash);
                    }, 100);
                }
            }
            
            showMessage(text, color = 'white') {
                const message = document.createElement('div');
                message.style.position = 'fixed';
                message.style.top = '30%';
                message.style.left = '50%';
                message.style.transform = 'translate(-50%, -50%)';
                message.style.color = color;
                message.style.fontSize = '24px';
                message.style.fontWeight = 'bold';
                message.style.textShadow = '2px 2px 4px rgba(0,0,0,0.8)';
                message.style.zIndex = '1000';
                message.style.pointerEvents = 'none';
                message.textContent = text;
                document.body.appendChild(message);
                
                setTimeout(() => {
                    document.body.removeChild(message);
                }, 2000);
            }
            
            takeDamage(damage) {
                this.health -= damage;
                if (this.health < 0) this.health = 0;

                // Visual feedback - flash screen red
                const flash = document.createElement('div');
                flash.style.position = 'fixed';
                flash.style.top = '0';
                flash.style.left = '0';
                flash.style.width = '100%';
                flash.style.height = '100%';
                flash.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
                flash.style.pointerEvents = 'none';
                flash.style.zIndex = '1000';
                document.body.appendChild(flash);

                setTimeout(() => {
                    document.body.removeChild(flash);
                }, 200);

                if (this.health <= 0) {
                    this.die();
                }
            }

            heal(amount) {
                this.health += amount;
                if (this.health > this.maxHealth) {
                    this.health = this.maxHealth;
                }

                // Visual feedback - flash screen green
                const flash = document.createElement('div');
                flash.style.position = 'fixed';
                flash.style.top = '0';
                flash.style.left = '0';
                flash.style.width = '100%';
                flash.style.height = '100%';
                flash.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
                flash.style.pointerEvents = 'none';
                flash.style.zIndex = '1000';
                document.body.appendChild(flash);

                setTimeout(() => {
                    document.body.removeChild(flash);
                }, 200);
            }
            
            die() {
                this.isDead = true;
                
                // Create death screen
                const deathScreen = document.createElement('div');
                deathScreen.style.position = 'fixed';
                deathScreen.style.top = '0';
                deathScreen.style.left = '0';
                deathScreen.style.width = '100%';
                deathScreen.style.height = '100%';
                deathScreen.style.backgroundColor = 'black';
                deathScreen.style.zIndex = '2000';
                deathScreen.style.display = 'flex';
                deathScreen.style.flexDirection = 'column';
                deathScreen.style.justifyContent = 'center';
                deathScreen.style.alignItems = 'center';
                
                const gameOver = document.createElement('div');
                gameOver.style.color = 'red';
                gameOver.style.fontSize = '64px';
                gameOver.style.fontWeight = 'bold';
                gameOver.style.marginBottom = '30px';
                gameOver.textContent = 'GAME OVER';
                
                const stats = document.createElement('div');
                stats.style.color = 'white';
                stats.style.fontSize = '20px';
                stats.style.textAlign = 'center';
                stats.style.marginBottom = '40px';
                stats.innerHTML = `
                    <div>Orcs Killed: ${this.inventory.kills}</div>
                    <div>Swords Collected: ${this.inventory.swords}</div>
                    <div>Final Score: ${this.inventory.score}</div>
                `;
                
                const restart = document.createElement('button');
                restart.style.padding = '15px 30px';
                restart.style.fontSize = '24px';
                restart.style.backgroundColor = '#ff4444';
                restart.style.color = 'white';
                restart.style.border = 'none';
                restart.style.borderRadius = '10px';
                restart.style.cursor = 'pointer';
                restart.textContent = 'RESTART GAME';
                restart.onclick = () => location.reload();
                
                deathScreen.appendChild(gameOver);
                deathScreen.appendChild(stats);
                deathScreen.appendChild(restart);
                document.body.appendChild(deathScreen);
                
                // Hide UI
                document.getElementById('ui').style.display = 'none';
                document.getElementById('inventory').style.display = 'none';
                document.getElementById('crosshair').style.display = 'none';
                document.getElementById('instructions').style.display = 'none';
            }
            
            update(deltaTime) {
                if (this.isDead) return;

                // Don't update movement if inventory is open
                if (this.inventory.isOpen) return;

                // Apply gravity
                this.velocity.y -= 25 * deltaTime;

                // Check if running
                const wantsToRun = this.keys['KeyR'] &&
                    (this.keys['KeyW'] || this.keys['KeyS'] || this.keys['KeyA'] || this.keys['KeyD']);
                this.isRunning = wantsToRun && this.stamina > 5; // Lower stamina requirement
                
                // Update stamina with smoother calculations
                if (this.isRunning) {
                    this.stamina -= 15 * deltaTime; // Consistent stamina drain
                    if (this.stamina <= 0) {
                        this.stamina = 0;
                        this.isRunning = false;
                    }
                } else {
                    this.stamina += 30 * deltaTime; // Consistent stamina recovery
                    if (this.stamina >= this.maxStamina) {
                        this.stamina = this.maxStamina;
                    }
                }

                // Clamp stamina to valid range to prevent glitches
                this.stamina = Math.max(0, Math.min(this.maxStamina, this.stamina));
                
                // Movement
                const forward = new THREE.Vector3(0, 0, -1);
                const right = new THREE.Vector3(1, 0, 0);
                
                forward.applyEuler(new THREE.Euler(0, this.rotation.y, 0));
                right.applyEuler(new THREE.Euler(0, this.rotation.y, 0));
                
                const moveVector = new THREE.Vector3();
                
                if (this.keys['KeyW']) moveVector.add(forward);
                if (this.keys['KeyS']) moveVector.sub(forward);
                if (this.keys['KeyA']) moveVector.sub(right);
                if (this.keys['KeyD']) moveVector.add(right);
                
                if (moveVector.length() > 0) {
                    moveVector.normalize();
                    const speed = this.isRunning ? 90 : 60; // Walk at old run speed, run even faster!
                    moveVector.multiplyScalar(speed * deltaTime);
                    this.velocity.x = moveVector.x;
                    this.velocity.z = moveVector.z;
                } else {
                    this.velocity.x *= 0.95; // Even less friction for super smooth movement
                    this.velocity.z *= 0.95;
                }
                
                // Jump
                if (this.keys['Space'] && this.onGround) {
                    this.velocity.y = 12; // Higher jump for more dynamic movement
                    this.onGround = false;
                }
                
                // Store old position for collision rollback
                const oldPosition = this.position.clone();

                // Update position
                this.position.add(this.velocity.clone().multiplyScalar(deltaTime));

                // Check building collisions
                if (world.checkBuildingCollision(this.position)) {
                    this.position.copy(oldPosition); // Rollback if collision
                }

                // Check world boundaries
                world.checkWorldBounds(this.position);

                // Ground collision
                const groundHeight = getHeightAt(Math.floor(this.position.x), Math.floor(this.position.z)) + 1;
                if (this.position.y <= groundHeight) {
                    this.position.y = groundHeight;
                    this.velocity.y = 0;
                    this.onGround = true;
                } else {
                    this.onGround = false;
                }

                // Update camera
                camera.position.copy(this.position);
                camera.position.y += this.height - 0.2;
                camera.rotation.set(this.rotation.x, this.rotation.y, 0);
            }
        }
        
        // Lighting system
        function updateLighting() {
            gameTime += 0.0005;
            if (gameTime > 1) gameTime = 0;
            
            const hour = Math.floor(gameTime * 24);
            const minute = Math.floor((gameTime * 24 - hour) * 60);
            
            document.getElementById('time').textContent = 
                `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            
            // Sun lighting
            const sunAngle = (gameTime - 0.25) * Math.PI * 2;
            const sunIntensity = Math.max(0.2, Math.sin(sunAngle) * 0.8 + 0.2);
            
            // Update directional light
            scene.children.forEach(child => {
                if (child.type === 'DirectionalLight') {
                    child.intensity = sunIntensity;
                    child.position.set(
                        Math.cos(sunAngle) * 100,
                        Math.sin(sunAngle) * 100,
                        0
                    );
                }
            });
            
            // Sky color
            const skyBrightness = Math.max(0.3, sunIntensity);
            const skyColor = new THREE.Color().setHSL(0.55, 0.6, skyBrightness);
            scene.background = skyColor;
            scene.fog.color = skyColor;
        }
        
        // Initialize game
        function init() {
            try {
                // Create scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);
                scene.fog = new THREE.Fog(0x87CEEB, 10, 100);
                
                // Create camera
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 200);
                
                // Create renderer
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0x87CEEB);
                document.getElementById('gameContainer').appendChild(renderer.domElement);
                
                // Create lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(50, 50, 50);
                scene.add(directionalLight);
                
                // Create world
                world = new World();
                
                // Create player
                player = new Player();

                // Initialize item icon renderer
                itemIconRenderer = new ItemIconRenderer();

                // Hide loading, show UI
                document.getElementById('loading').style.display = 'none';
                document.getElementById('ui').style.display = 'block';
                document.getElementById('crosshair').style.display = 'block';
                document.getElementById('instructions').style.display = 'block';
                document.getElementById('inventory').style.display = 'block';
                
                // Initialize inventory UI and add starter items
                player.inventory.addItem('SWORD', 1);
                player.inventory.addItem('HEALTH_POTION', 3);
                player.inventory.addItem('DIRT_BLOCK', 10);
                player.inventory.addItem('WOOD_BLOCK', 5);
                player.inventory.addItem('PICKAXE', 1);
                player.inventory.updateUI();

                console.log('Game initialized successfully!');
                
                // Start game loop
                animate();
                
            } catch (error) {
                console.error('Failed to initialize game:', error);
                document.getElementById('loading').textContent = 'Failed to load. Please refresh.';
            }
        }
        
        // Game loop
        function animate() {
            requestAnimationFrame(animate);
            
            try {
                const currentTime = performance.now();
                const deltaTime = Math.min((currentTime - lastTime) / 1000, 1/30);
                lastTime = currentTime;
                
                if (deltaTime > 0 && !player.isDead) {
                    // Update player
                    player.update(deltaTime);
                    
                    // Update world chunks
                    world.updateChunks(player.position.x, player.position.z);
                    
                    // Update enemies
                    world.updateEnemies(deltaTime, player.position);
                    
                    // Update lighting
                    updateLighting();
                    
                    // Update UI
                    frameCount++;
                    if (frameCount % 30 === 0) { // Update UI more frequently for smoother stamina
                        const fps = Math.round(1 / deltaTime);
                        document.getElementById('fps').textContent = fps;

                        document.getElementById('health').textContent = Math.floor(player.health);

                        // Update stamina bar and text
                        const displayStamina = Math.max(0, Math.min(100, Math.round(player.stamina)));
                        const staminaPercent = displayStamina / 100;

                        // Update stamina bar width
                        const staminaBar = document.getElementById('staminaBar');
                        staminaBar.style.width = `${staminaPercent * 100}%`;

                        // Update stamina bar color based on level and running state
                        if (player.isRunning && player.stamina > 5) {
                            staminaBar.style.background = '#00ff00'; // Solid green when running
                            staminaBar.style.boxShadow = '0 0 8px rgba(0, 255, 0, 0.6)'; // Glowing effect
                        } else if (player.stamina < 20) {
                            staminaBar.style.background = '#ff0000'; // Red when low
                            staminaBar.style.boxShadow = '0 0 8px rgba(255, 0, 0, 0.6)'; // Red glow
                        } else if (player.stamina < 50) {
                            staminaBar.style.background = 'linear-gradient(to right, #ffff00, #ff8800)'; // Yellow to orange
                            staminaBar.style.boxShadow = '0 0 6px rgba(255, 255, 0, 0.4)'; // Yellow glow
                        } else {
                            staminaBar.style.background = 'linear-gradient(to right, #00ff00, #88ff00)'; // Green gradient
                            staminaBar.style.boxShadow = '0 0 4px rgba(0, 255, 0, 0.3)'; // Subtle green glow
                        }

                        // Update stamina text
                        document.getElementById('staminaText').textContent = displayStamina;

                        const biome = getBiome(Math.floor(player.position.x), Math.floor(player.position.z));
                        document.getElementById('biome').textContent = biome.charAt(0).toUpperCase() + biome.slice(1);

                        document.getElementById('orcs').textContent = world.orcs.length;
                    }
                }
                
                // Always render
                renderer.render(scene, camera);
                
            } catch (error) {
                console.error('Error in game loop:', error);
            }
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Character Creation Event Handlers
        function initCharacterCreation() {
            const modal = document.getElementById('characterCreationModal');
            const nameInput = document.getElementById('characterName');
            const classOptions = document.querySelectorAll('.class-option');
            const genderOptions = document.querySelectorAll('.gender-option');
            const startButton = document.getElementById('startAdventure');

            let selectedClass = null;
            let selectedGender = null;

            // Class selection
            classOptions.forEach(option => {
                option.addEventListener('click', () => {
                    classOptions.forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    selectedClass = option.dataset.class;
                    checkCanStart();
                });
            });

            // Gender selection
            genderOptions.forEach(option => {
                option.addEventListener('click', () => {
                    genderOptions.forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    selectedGender = option.dataset.gender;
                    checkCanStart();
                });
            });

            // Name input
            nameInput.addEventListener('input', checkCanStart);

            function checkCanStart() {
                const hasName = nameInput.value.trim().length > 0;
                const hasClass = selectedClass !== null;
                const hasGender = selectedGender !== null;

                startButton.disabled = !(hasName && hasClass && hasGender);
            }

            // Start adventure button
            startButton.addEventListener('click', () => {
                const name = nameInput.value.trim();
                if (name && selectedClass && selectedGender) {
                    gameState.createCharacter(name, selectedClass, selectedGender);
                    modal.style.display = 'none';
                    startGame();
                }
            });
        }

        function startGame() {
            // Show story panel toggle
            document.getElementById('storyToggle').style.display = 'block';

            // Initialize story panel toggle
            const storyToggle = document.getElementById('storyToggle');
            const storyPanel = document.getElementById('storyPanel');

            storyToggle.addEventListener('click', () => {
                const isVisible = storyPanel.style.display === 'block';
                storyPanel.style.display = isVisible ? 'none' : 'block';
                storyToggle.textContent = isVisible ? '📖 Story' : '📖 Hide';
            });

            // Start the 3D game
            gameState.gameStarted = true;
            init();
        }

        // Modified init function to work with character creation
        function init() {
            if (!gameState.isCharacterCreated) {
                console.log('Character not created yet');
                return;
            }

            try {
                // Create scene
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);
                scene.fog = new THREE.Fog(0x87CEEB, 10, 100);

                // Create camera
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 200);

                // Create renderer
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0x87CEEB);
                document.getElementById('gameContainer').appendChild(renderer.domElement);

                // Create lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(50, 50, 50);
                scene.add(directionalLight);

                // Create world
                world = new World();

                // Create player
                player = new Player();

                // Initialize item icon renderer
                itemIconRenderer = new ItemIconRenderer();

                // Hide loading, show UI
                document.getElementById('loading').style.display = 'none';
                document.getElementById('ui').style.display = 'block';
                document.getElementById('crosshair').style.display = 'block';
                document.getElementById('instructions').style.display = 'block';
                document.getElementById('inventory').style.display = 'block';

                // Initialize inventory UI and add starter items
                player.inventory.addItem('SWORD', 1);
                player.inventory.addItem('HEALTH_POTION', 3);
                player.inventory.addItem('DIRT_BLOCK', 10);
                player.inventory.addItem('WOOD_BLOCK', 5);
                player.inventory.addItem('PICKAXE', 1);
                player.inventory.updateUI();

                console.log('Game initialized successfully!');

                // Start game loop
                animate();

            } catch (error) {
                console.error('Failed to initialize game:', error);
                document.getElementById('loading').textContent = 'Failed to load. Please refresh.';
            }
        }

        // Start the game
        window.addEventListener('load', () => {
            initCharacterCreation();
            // Don't auto-start the game anymore - wait for character creation
        });
        
    </script>
</body>
</html>